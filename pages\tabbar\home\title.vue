<template>
	<view>
		<u-navbar  :title="title" title-color="#333333"> 
			<view class="slot-wrap" @tap="handleBatchRead"> 
				<image
					src="/static/img/trash.png"
					mode="scaleToFill"
				/>
				<text>清扫未读</text>
			</view>
		</u-navbar>
		<view class="quick-entry" v-if="false">
			<view class="entry-item">
				<view class="icon-wrap">
					<image
						src="/static/img/ico1A.png"
						mode="scaleToFill"
					/>
					<view v-if="unreadCount > 0" class="badge">{{ unreadCount }}</view>
				</view>
				<text>订单</text>
			</view>
			<view class="entry-item">
				<view class="icon-wrap">
					<image
						src="/static/img/ico2A.png"
						mode="scaleToFill"
					/>
					<!-- <view v-if="unreadCount > 0" class="badge">{{ unreadCount }}</view> -->
				</view>
				<text>互动消息</text>
			</view>
			<view class="entry-item">
				<view class="icon-wrap">
					<image
						src="/static/img/ico3A.png"
						mode="scaleToFill"
					/>
				</view>
				<text>客服</text>
			</view>
		</view>
		<view>
			<u-tabs :list="list" :is-scroll="false" :current="current" @change="change" active-color="#FF5134"></u-tabs>
			<!-- <view v-if="showLoading" style="width:500rpx;margin:0 auto;text-align: center;height:800rpx;line-height: 800rpx;">
				<u-loading mode="flower" ></u-loading>
				<text>正在加载中</text>
			</view> -->
			<view v-if="current == 0">
				<view v-if="lists.length === 0" style="margin-top: 300rpx;">
					<u-empty  mode="message"></u-empty>
				</view>
				<view v-else>
					<view v-for="(item,index) in lists" :key="index" class="msg-item" @click="linkMsgDetail(item)">
						<view class="msg-icon">
							<image src="/static/img/ico1.png" mode="aspectFill" />
						</view>
						<view class="msg-content">
							<view class="msg-title">{{ item.messageTitle }}</view>
							<view class="msg-sub">{{ item.messageContent }}</view>
						</view>
						<view class="msg-right">
							<view class="msg-time">{{ item.gmtCreate }}</view>
							<!-- <view v-if="item.status == 'UN_READY'" class="msg-badge">{{ unreadCount }}</view> -->
						</view>
					</view>
				</view>
			</view>
			<view v-if="current == 1">
				<view v-if="lists.length === 0" style="margin-top: 300rpx;">
					<u-empty  mode="message"></u-empty>
				</view>
				<view v-else>
					<view v-for="(item,index) in lists" :key="index" class="msg-item" @click="linkMsgDetail(item)">
						<view class="msg-icon">
							<image src="/static/img/ico1.png" mode="aspectFill" />
						</view>
						<view class="msg-content">
							<view class="msg-title">{{ item.messageTitle }}</view>
							<view class="msg-sub">{{ item.messageContent }}</view>
						</view>
						<view class="msg-right">
							<view class="msg-time">{{ item.gmtCreate }}</view>
							<!-- 已读不显示红点 -->
						</view>
					</view>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		messages,
		editMessages,
		batchReadMessages
	} from "@/api/message.js"
	export default {
		data() {
			return {
				showLoading:true,
				unreadCount: 0,
				orderCount: 3,
				params: {
					pageSize: 20,
					pageNumber: 1,
					memberId: "",
					messageId: "",
					status:"UN_READY"
				},
				loadText: {
					loadmore: '轻轻上拉',
					loading: '努力加载中',
					nomore: '实在没有了'
				},
				list: [{
					name: "未读"
				}, {
					name: "已读"
				}],
				current: 0,
				lists: [],
				status: "loadmore",
				title: "系统消息"
			}
		},
		onShow() {
			this.getMessage()
			this.getUnreadCount()
		},
		onReachBottom() {
			this.params.pageNumber++;
			this.statuss = "loading";
			this.getMessage()
		},
		methods: {
			// 获取未读消息数量
			getUnreadCount() {
				const params = {
					pageSize: 1,
					pageNumber: 1,
					memberId: this.$options.filters.isLogin().id,
					status: "UN_READY"
				};
				
				messages(params).then(res => {
					if (res.data.success && res.data.result && res.data.result.total) {
						this.unreadCount = res.data.result.total;
						this.list[0].name = `未读(${this.unreadCount})`;
						this.title = this.unreadCount > 0 ? `系统消息(${this.unreadCount})` : "系统消息";
					} else {
						this.unreadCount = 0;
						this.list[0].name = "未读";
						this.title = "系统消息";
					}
				}).catch(error => {
					console.error('获取未读消息数量失败:', error);
					this.unreadCount = 0;
					this.title = "系统消息";
					this.list[0].name = "未读";
				});
			},
			linkMsgDetail(v) {
				console.log(v);
				if (v.messageStatus == 'UN_READY') {
					let params = {}
					params.messageId = v.id
					editMessages(v.id, params).then(res => {
						if (res.data.success) {
                            console.log( this.lists)
                            this.lists.forEach((item,index)=>{
                                console.log(item)
                                if(item.id == v.id){
                                    this.lists.splice(index, 1)
                                }
                            })
							// 消息标记为已读后，重新获取未读数量
							this.getUnreadCount()
						}
					})
				}
				
				uni.navigateTo({
					url:`/pages/tabbar/home/<USER>
				})
				

			},
			/**
			 * 返回
			 */
			back() {
				if (getCurrentPages().length == 1) {
					uni.switchTab({
						url: "/pages/tabbar/home/<USER>",
					});
				} else {
					uni.navigateBack();
				}
			},
			change(e) {
				this.showLoading = true;
				console.log(e)
				this.current = e;
				if (e == 0) {
					this.params.status = "UN_READY"
					this.params.pageNumber = 1;
				} else if (e == 1) {
					this.params.status = "ALREADY_READY"
					this.params.pageNumber = 1;
				}
				this.lists = []
				this.getMessage()
			},
			getMessage() {
				this.params.memberId = this.$options.filters.isLogin().id;
				
				messages(this.params).then(res => {
					console.log(res)
					if (res.data.success) {
						this.showLoading = false
						if (res.data.result.records == '') {
							console.log(11111)
							this.status = "nomore"
						}
						res.data.result.records.forEach(item => {
							this.lists.push(item)
							let obj = {};
							this.lists = this.lists.reduce(
								(cur, next) => {
									//对象去重
									if (next.id != undefined) {
										obj[next.id] ?
											"" :
											(obj[next.id] = true && cur.push(next));
									}
									console.log(cur);
									return cur;
								},
								[]
							)
						})

					}
				})
			},
			async handleBatchRead() {
				try {
					await batchReadMessages();
					uni.showToast({ title: '全部已读', icon: 'none' });
					this.getMessage();
					this.getUnreadCount();
				} catch (e) {
					uni.showToast({ title: '操作失败', icon: 'none' });
				}
			},
		},
	}
</script>
<style>
	.foot {
		position: fixed;
		bottom: 0;
	}
	.slot-wrap {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		
		padding-right: 32rpx;
		font-weight: 400;
		font-size: 28rpx;
		color: #666666;
		position: absolute;
		right: 0;
		
	}
	.slot-wrap > image {
			width: 30rpx;
			height: 30rpx;
	}
	.quick-entry {
		display: flex;
		justify-content: space-around;
		align-items: center;
		background: #fafafa;
		border-radius: 20rpx;
		margin: 20rpx auto;
		padding: 20rpx 0;
		width: 686rpx;
		height: 218rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 20rpx 20rpx;
	}
	.entry-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
	}
	.icon-wrap {
		position: relative;
		
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
		
	}
	.icon-wrap >image {
		width: 100rpx;
		height: 100rpx;
	}
	.badge {
		position: absolute;
		top: -10rpx;
		right: -10rpx;
		background: #FF3B30;
		color: #fff;
		border-radius: 50%;
		font-size: 22rpx;
		min-width: 40rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
		padding: 0 8rpx;
		box-sizing: border-box;
		z-index: 1;
	}
	.msg-item {
		display: flex;
		align-items: center;
		padding: 24rpx 0;
		/* border-bottom: 1px solid #f2f2f2; */
		background: #F7F7F7;
		position: relative;
	}
	.msg-icon {
		
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 24rpx;
		margin-right: 24rpx;
		flex-shrink: 0;
	}
	.msg-icon image {
		width: 88rpx;
		height: 88rpx;
	}
	.msg-content {
		flex: 1;
		min-width: 0;
	}
	.msg-title {
		font-size: 30rpx;
		color: #222;
		font-weight: 500;
		margin-bottom: 8rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.msg-sub {
		font-size: 24rpx;
		color: #888;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.msg-right {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		margin-right: 24rpx;
		min-width: 80rpx;
	}
	.msg-time {
		font-size: 22rpx;
		color: #bbb;
		margin-bottom: 12rpx;
	}
	.msg-badge {
		background: #FF3B30;
		color: #fff;
		border-radius: 50%;
		font-size: 22rpx;
		min-width: 40rpx;
		height: 40rpx;
		line-height: 40rpx;
		text-align: center;
		padding: 0 8rpx;
		box-sizing: border-box;
	}
</style>
