<template>
  <view class="forgot-password-page">
    <u-navbar :is-back="true" title="修改密码" :border-bottom="false"></u-navbar>

    <view class="content">
      <text class="info-text">请输入信息修改密码</text>

      <view class="input-item">
        <u-input
          :custom-style="inputStyle"
          :placeholder-style="placeholderStyle"
          placeholder="请输入手机号"
          v-model="mobile"
          :maxlength="11"
        />
      </view>

      <view class="input-item">
        <u-input
          :custom-style="inputStyle"
          :placeholder-style="placeholderStyle"
          placeholder="请输入图形验证码"
          v-model="graphicCode"
          :maxlength="5"
        />
        <image 
          v-if="captchaImage" 
          class="graphic-code-image" 
          :src="captchaImage" 
          @click="getGraphicCaptcha"
          mode="aspectFit"
        />
        <text v-else class="graphic-code" @click="getGraphicCaptcha">点击获取</text>
      </view>


      <view class="input-item">
        <u-input
          :custom-style="inputStyle"
          :placeholder-style="placeholderStyle"
          placeholder="请输入手机验证码"
          v-model="mobileCode"
          :maxlength="6"
        />
        <view class="fetch-code-text">
          <template v-if="$refs.uCode && !$refs.uCode.canGetCode">
            <span :style="{ color: '#FF5134' }">{{ countdownNum }}</span>
            <span :style="{ color: '#999999' }">{{ countdownUnit }}{{ countdownSuffix }}</span>
          </template>
          <template v-else>
            <span @tap="fetchMobileCode" :style="{ color: '#FF5134' }">获取验证码</span>
          </template>

          <u-verification-code
            change-text="x秒后重新获取"
            end-text="重新获取验证码"
            unique-key="forgot-password-mobile-code"
            :seconds="seconds"
            @end="end"
            @start="start"
            ref="uCode"
            @change="codeChange"
            style="display: none;"
          >
          </u-verification-code>
        </view>
      </view>


      <u-input
        class="input-wrapper"
        :custom-style="inputStyle"
        :placeholder-style="placeholderStyle"
        placeholder="请输入13-20位新密码"
        v-model="newPassword"
        type="password"
        :maxlength="20"
      />


      <u-input
        class="input-wrapper"
        :custom-style="inputStyle"
        :placeholder-style="placeholderStyle"
        placeholder="请再次输入13-20位新密码"
        v-model="confirmPassword"
        type="password"
        :maxlength="20"
      />

      <text class="password-tip">温馨提示: 密码必须为13-20位，需同时包含大小写字母、数字、特殊字符(!@#$%^&*?)</text>

      <view
        :class="['reset-button', { 'disable': !canResetPassword }]"
        @click="resetPassword"
      >
        重置密码
      </view>
    </view>
  </view>
</template>

<script>
import UInput from '../../uview-ui/components/u-input/u-input.vue';
import UVerificationCode from '../../uview-ui/components/u-verification-code/u-verification-code.vue';
import UNavbar from '../../uview-ui/components/u-navbar/u-navbar.vue';
import { getCaptcha, checkGraphicCaptcha } from '@/api/common.js';
import { sendMobile } from '@/api/login.js';
import { forgetPassword } from '@/api/login.js';
import api from '@/config/api.js';
import { md5 } from "@/utils/md5.js";
export default {
  components: {
    UInput,
    UVerificationCode,
    UNavbar
  },
  data() {
    return {
      mobile: '',
      graphicCode: '',
      staticGraphicCode: '', 
      captchaImage: '',
      mobileCode: '',
      newPassword: '',
      confirmPassword: '',
      countdownNum: '',
      countdownUnit: '秒后',
      countdownSuffix: '重新获取',
      seconds: 60,
      inputStyle: {
        height: '100rpx',
        'border-bottom': '1rpx solid #EEEEEE',
        'letter-spacing': '1rpx',
        'font-size': '32rpx',
        'line-height': '32rpx',
        color: '#333',
      },
      placeholderStyle: 'font-size: 32rpx;line-height: 32rpx;color: rgba(153,153,153,0.55);',
    };
  },
  computed: {
    canResetPassword() {
      return (
        this.mobile &&
        /^1[3-9]\d{9}$/.test(this.mobile) &&
        this.graphicCode.length === 5 &&
        this.mobileCode.length === 6 &&
        this.newPassword.length >= 13 &&
        this.newPassword.length <= 20 &&
        this.newPassword === this.confirmPassword &&
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*?])[A-Za-z\d!@#$%^&*?]{13,20}$/.test(this.newPassword) 
      );
    },
  },
  methods: {
    // 获取图形验证码
    async getGraphicCaptcha() {
      try {
        // 这里使用封装的getCaptcha方法app就不好使
        // const { data } = await getCaptcha();
        uni.request({
          url: `${api.common}/common/captcha/getCaptcha`,
          method: 'get',
          responseType: 'arraybuffer',
          success: res => {
            let result = res.data;
            this.captchaImage = 'data:image/png;base64,' + btoa(new Uint8Array(result).reduce((datas, byte) => datas + String.fromCharCode(byte), ''));
          },
        });
      } catch (error) {
        console.error('获取图形验证码失败:', error);
      }
    },
    async fetchMobileCode() {
      if (!this.mobile) {
        uni.showToast({ title: '请输入手机号', icon: 'none' });
        return;
      }
      
      if (!/^1[3-9]\d{9}$/.test(this.mobile)) {
        uni.showToast({ title: '请输入正确的手机号', icon: 'none' });
        return;
      }
      
      if (!this.graphicCode || this.graphicCode.length !== 5) {
        uni.showToast({ title: '请输入5位图形验证码', icon: 'none' });
        return;
      }

      // 先校验图形验证码
      try {
        const checkRes = await checkGraphicCaptcha( 'FIND_USER',this.graphicCode);
        if (checkRes.statusCode !== 200 || !checkRes.data.success) {
          uni.showToast({ title: checkRes.data.message || '图形验证码错误', icon: 'none' });
          return;
        }
      } catch (e) {
        uni.showToast({ title: '图形验证码校验失败', icon: 'none' });
        return;
      }
      
      if (this.$refs.uCode.canGetCode) {
        // 调用发送验证码接口
        sendMobile(this.mobile, 'FIND_USER').then(res => {
          if (res.data.success) {
            this.$refs.uCode.start();
            uni.showToast({ title: '验证码已发送', icon: 'none' });
          } else {
            uni.showToast({ title: res.data.message || '发送失败', icon: 'none' });
          }
        }).catch(error => {
          console.error('发送验证码失败:', error);
          uni.showToast({ title: '发送验证码失败', icon: 'none' });
        });
      } else {
        uni.showToast({ title: '请倒计时结束后再发送', icon: 'none' });
      }
    },
    resetPassword() {
      if (!this.mobile) {
        uni.showToast({ title: '请输入手机号', icon: 'none' });
        return;
      }
      
      if (!/^1[3-9]\d{9}$/.test(this.mobile)) {
        uni.showToast({ title: '请输入正确的手机号', icon: 'none' });
        return;
      }
      
      if (!this.graphicCode || this.graphicCode.length !== 5) {
        uni.showToast({ title: '请输入5位图形验证码', icon: 'none' });
        return;
      }
     
      // if (this.graphicCode !== this.staticGraphicCode) {
      //   uni.showToast({ title: '图形验证码错误', icon: 'none' });
      //   return;
      // }
      
      if (!this.mobileCode || this.mobileCode.length !== 6) {
        uni.showToast({ title: '请输入6位手机验证码', icon: 'none' });
        return;
      }
      
      if (!this.newPassword) {
        uni.showToast({ title: '请输入新密码', icon: 'none' });
        return;
      }
      
      if (this.newPassword.length < 13 || this.newPassword.length > 20) {
        uni.showToast({ title: '密码长度必须为13-20位', icon: 'none' });
        return;
      }
      
      if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*?])[A-Za-z\d!@#$%^&*?]{13,20}$/.test(this.newPassword)) {
        uni.showToast({ title: '密码必须包含大小写字母、数字、特殊字符', icon: 'none' });
        return;
      }
      
      if (this.newPassword !== this.confirmPassword) {
        uni.showToast({ title: '两次输入的密码不一致', icon: 'none' });
        return;
      }
      const encryptedPassword = md5(this.newPassword);
      // 调用重置密码接口
      forgetPassword({
        mobile: this.mobile,
        password: encryptedPassword,
        code: this.mobileCode
      }).then(res => {
        if (res.data && res.data.success) {
          uni.showToast({ title: '重置密码成功', icon: 'none' });
          setTimeout(() => {
            uni.reLaunch({
              url:'/pages/passport/login',
            })
          }, 1000);
          
        } else {
          uni.showToast({ title: res.data.message || '重置密码失败', icon: 'none' });
        }
      }).catch(err => {
        uni.showToast({ title: '重置密码失败', icon: 'none' });
      });
    },
    start() {
     
    },
    end() {
     
    },
    codeChange(text) {
      const match = text.match(/^(\d+)(秒后)(.*)/);
      if (match) {
        this.countdownNum = match[1];
        this.countdownUnit = match[2];
        this.countdownSuffix = match[3];
      } else {
        this.countdownNum = '';
        this.countdownUnit = '';
        this.countdownSuffix = text;
      }
    },
  },
  mounted() {
    this.getGraphicCaptcha();
  },
};
</script>

<style lang="scss" scoped>
.forgot-password-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #fff;
}

.content {
  padding: 0 40rpx;
  flex: 1;
}

.info-text {
  display: block;
  font-size: 36rpx;
  color: #333;
  margin-top: 80rpx;
  margin-bottom: 80rpx;
}

.input-item {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #EEEEEE;
  // margin-bottom: 40rpx;

  /deep/ .u-input {
    flex: 1;
    border-bottom: none !important;
  }
}

/deep/ .u-input__input {
  border-bottom: none !important;
}

.graphic-code {
  font-size: 32rpx;
  color: #007aff; 
  margin-left: 20rpx;
}

.fetch-code-text {
  font-size: 28rpx;
  color: #FF5134;
  margin-left: 20rpx;
  white-space: nowrap; 
}

.password-tip {
  display: block;
  font-size: 24rpx;
  color: #999999;
  margin-top: 40rpx;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.reset-button {
  border-radius: 100px;
  width: 670rpx;
  height: 100rpx;
  font-size: 36rpx;
  line-height: 100rpx;
  text-align: center;
  color: #ffffff;
  margin-left: auto;
  margin-right: auto;
  background: linear-gradient(57.72deg, #ff8a19 18.14%, #ff5e00 98.44%); 
}

.disable {
  // background: linear-gradient(90deg, #ffdcba 2.21%, #ffcfb2 99.86%); 
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  opacity: .5;
}
.input-wrapper {
  border-bottom: 1rpx solid #f5f5f5;
}

.graphic-code-image {
  width: 240rpx;
  height: 100rpx;
  margin-left: 20rpx;
  border-radius: 8rpx;
  cursor: pointer;
}
</style> 