<template>
  <view class="container">
    <view class="main-box">
      <view class="warn-box">
        <image
          class="warn-icon"
          src="/static/img/warn.png"
          mode="aspectFit"
        ></image>
        <view class="warn-title">账号注销后，以下权益和信息会发生变化</view>
      </view>
      <view class="info_box">
        <view class="info-box">
          <view class="info-title">身份、账号信息将会被停用且无法恢复</view>
          <view class="info-list">
            <view class="info-item">将清除购物车记录</view>
            <view class="info-item">将清楚您的账号信用分</view>
          </view>
        </view>
        <view class="info-box" style="margin-top: 40rpx;">
          <view class="info-title">订单记录将被清空</view>
          <view class="info-list">
            <view class="info-item">请确保所有订单完成且无任何异议</view>
            <view class="info-item"
              >
              账号注销后管理订单时如有可能产生的资金退款等将会视作自动放弃</view
            >
          </view>
        </view>
        <view class="info-box" style="margin-top: 40rpx;margin-bottom: 0;">
          <view class="info-title">所有优惠券信息将被取消</view>
        </view>
      </view>
    </view>
    <view class="btn-box">
      <button
        class="cancel-btn"
        :disabled="countdown > 0"
        @click="onReadProtocol"
      >
        <text style="color: #fff;" v-if="countdown > 0"> 请仔细阅读相关协议</text>
        <text v-if="countdown <= 0">下一步</text>
        <text style="color: #fff;" v-if="countdown > 0">({{ countdown }}s)</text>
      </button>
    </view>
    <u-popup v-model="showSms" mode="center" border-radius="16" width="600rpx">
      <view class="sms-modal">
        <view class="sms-header">
          <text class="sms-title">短信验证</text>
          <u-icon name="close" size="32" @click="showSms = false" style="position:absolute;right:0;top:0;"></u-icon>
        </view>
        <view class="sms-desc">验证码将发送至手机{{ phone }}，请注意查收</view>
        <view class="sms-row">
          <input class="sms-input" v-model="smsCode" maxlength="6" placeholder="请输入验证码" placeholder-class="sms-placeholder" />
          <text class="sms-get" :class="{disabled: smsCountdown>0}" @click="getSmsCode">{{ smsCountdown>0 ? smsCountdown+'s后重发' : '获取验证码' }}</text>
        </view>
        <button class="sms-btn" @click="submitSms">下一步</button>
      </view>
    </u-popup>
    <u-modal
      v-model="showLogoff"
      title="提示"
      content="确认注销用户么？注销用户将无法再次登录并失去当前数据。"
      show-cancel-button
      @confirm="handleLogoff"
      confirm-color="#FF5134"
    />
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { sendMobile_Can } from '@/api/login';
import eventBus from '@/utils/eventBus';
import {logoffConfirm } from "@/api/login";
import storage from "@/utils/storage.js";
export default {
  data() {
    return {
      countdown: 60,
      timer: null,
      showSms: false,
      smsCode: '',
      smsCountdown: 0,
      smsTimer: null,
      showLogoff: false,
    };
  },
  computed: {
    ...mapState(['userInfo']),
    phone() {
      const mobile = this.userInfo.mobile || this.userInfo.phone || '';
      if (!mobile) return '';
      // 脱敏处理
      return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },
    rawPhone() {
      return this.userInfo.mobile || this.userInfo.phone || '';
    }
  },
  
  created() {
    eventBus.$on('show-logoff-modal', () => {
      this.showLogoff = true;
    });
  },
  beforeDestroy() {
    eventBus.$off('show-logoff-modal');
  },
  mounted() {
    this.startCountdown();
  },
  beforeDestroy() {
    clearInterval(this.timer);
    clearInterval(this.smsTimer);
  },

  methods: {
    startCountdown() {
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--;
        } else {
          clearInterval(this.timer);
        }
      }, 1000);
    },
    async handleLogoff() {
      // 注销逻辑
     
      storage.setAccessToken("");
      storage.setRefreshToken("");
      storage.setUserInfo({});
      // 重置服务授权状态
      uni.removeStorageSync('hasAgreedAuth');
      this.$store.commit('setHasAgreedAuth', false);
      // 注销成功后跳转到注销成功页面
      uni.reLaunch({ url: '/pages/mine/set/securityCenter/accountCancelSuccess' });

    },
    onReadProtocol() {
      if (this.countdown <= 0) {
        this.showSms = true;
      }
    },
    async getSmsCode() {
      if (this.smsCountdown > 0) return;
      if (!this.rawPhone || !/^1[3-9]\d{9}$/.test(this.rawPhone)) {
        uni.showToast({ title: '手机号无效', icon: 'none' });
        return;
      }
      try {
        await sendMobile_Can();
        uni.showToast({ title: '验证码已发送', icon: 'none' });
        this.smsCountdown = 60;
        this.smsTimer = setInterval(() => {
          if (this.smsCountdown > 0) {
            this.smsCountdown--;
          } else {
            clearInterval(this.smsTimer);
          }
        }, 1000);
      } catch (e) {
        uni.showToast({ title: '验证码发送失败', icon: 'none' });
      }
    },
    async submitSms() {
      if (!this.smsCode) {
        uni.showToast({ title: '请输入验证码', icon: 'none' });
        return;
      }
      // uni.showToast({ title: '验证成功', icon: 'none' });
      const res = await logoffConfirm(this.userInfo.mobile,this.smsCode);
      if (res.data.code !== 200) {
        uni.showToast({ title: res.data.message, icon: 'none' });
        return;
      }else {
        this.showSms = false;
        // 调用注销
        this.$options.filters.logoff();
      }
     
    },
  },
  onShow() {
    // 重新从 storage 取，保证 userInfo 最新
    this.$store.state.userInfo = require('@/utils/storage').default.getUserInfo();
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #fafafa;
  padding: 20rpx 32rpx;
}
.main-box {
}
.warn-box {
  background: #fff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 52rpx 0 40rpx 0;
  margin-bottom: 24rpx;
}
.warn-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 12rpx;
}
.warn-title {
  color: #333;
  font-size: 28rpx;
  text-align: center;
  font-weight: 500;
}
.info_box {
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx 0 40rpx 32rpx;
}
.info-box {
 
}
.info-title {
    font-weight: 500;
    font-size: 32rpx;
    color: #000000;
    margin-bottom: 20rpx;
}
.info-list {
  margin-left: 12rpx;
}
.info-item {
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  position: relative;
  padding-left: 32rpx;
  line-height: 1.7;
}
.info-item::before {
  content: '•';
  position: absolute;
  left: 0;
  top: 0;
  color: #999999;
  font-size: 32rpx;
  line-height: 1.7;
}
.btn-box {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 40rpx;
  display: flex;
  justify-content: center;
}
.cancel-btn {
  width: 670rpx;
  height: 100rpx;
  border-radius: 78rpx;
  line-height: 100rpx;
  text-align: center;
  color: #fff;
  font-size: 32rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  box-shadow: 0 4rpx 16rpx rgba(255, 124, 52, 0.15);
  border: none;
  font-weight: 400;
}
.cancel-btn[disabled] {
  opacity: 0.7;
}
.sms-modal {
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  min-width: 540rpx;
}
.sms-header {
  position: relative;
  text-align: center;
  margin-bottom: 20rpx;
}
.sms-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.sms-desc {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 26rpx;
  text-align: center;
}
.sms-row {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 40rpx;
}
.sms-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 28rpx;
  color: #222;
  padding: 20rpx 0;
}
.sms-placeholder {
  color: #cccccc;
  font-size: 28rpx;
}
.sms-get {
  color: #ff5134;
  font-size: 28rpx;
  margin-left: 20rpx;
}
.sms-get.disabled {
  color: #ccc;
}
.sms-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #FF5235 0%, #FF9500 100%);
  color: #fff;
  font-size: 32rpx;
  border-radius: 50rpx;
  border: none;
  margin-top: 20rpx;
  font-weight: 400;
}
</style>
