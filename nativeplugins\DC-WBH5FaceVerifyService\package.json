{"name": "DC-WBH5FaceVerifyService", "id": "DC-WBH5FaceVerifyService", "version": "2.0.0", "description": "WB H5 FACEVERIFY SDK uni-app 插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "DC-WBH5FaceVerifyService", "class": "com.tencent.cloud.huiyansdk.dc_wbh5faceservice.WBH5FaceVerifyServiceModule"}], "integrateType": "aar", "abis": ["<PERSON><PERSON><PERSON>", "armeabi-v7a", "arm64-v8a"], "minSdkVersion": "21", "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_WIFI_STATE", "android.permission.ACCESS_NETWORK_STATE", "android.permission.CAMERA", "android.permission.RECORD_AUDIO"]}}}