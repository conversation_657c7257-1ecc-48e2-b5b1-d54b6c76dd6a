<template>
  <view class="cashier-page">
    <!-- 顶部信息区域 -->
    <view class="top-card">
      <view class="top-title">扣款金额</view>
      <view class="top-amount">
        ¥<text class="top-amount-num">{{ amount }}</text>
      </view>
      <view class="top-pay-way">
        <view class="pay-way-title">还款账户</view>
        <view class="pay-way-item" v-if="selectedBankCard">
          {{ selectedBankCard.bankName }}({{ selectedBankCard.bankcardNumber ? selectedBankCard.bankcardNumber.slice(-4) : '' }})
        </view>
        <view class="pay-way-item" v-else>
          暂无银行卡
        </view>
      </view>
      <view class="pay-tips">
        预计2个工作日内完成
      </view>
      <!-- <view class="top-timer">
        支付剩余时间：
        <u-count-down
          :show-days="false"
          :show-border="false"
          font-size="28"
          color="#000"
          border-color="#fff"
          :timestamp="autoCancel"
        ></u-count-down>
      </view> -->
    </view>

    <!-- 底部确认支付按钮 -->
    <view class="pay-btn-box">
      <button class="pay-btn-gradient" @click="onConfirmPay">立即支付</button>
      <view class="security-tip">
        <image src="/static/financial/desc.png" mode="scaleToFill" />
        平台承诺保障您的信息安全
      </view>
    </view>
    
    <!-- 验证码弹窗 -->
    <u-popup v-model="showSms" mode="center" border-radius="16" width="600rpx">
      <view class="sms-modal">
        <view class="sms-header">
          <text class="sms-title">还款验证</text>
          <u-icon name="close" size="32" @click="showSms = false" style="position:absolute;right:0;top:0;"></u-icon>
        </view>
        <view class="sms-desc">验证码将发送至手机{{ phone }}，请注意查收</view>
        <view class="sms-row">
          <input class="sms-input" v-model="smsCode" maxlength="6" placeholder="请输入验证码" placeholder-class="sms-placeholder" />
          <text class="sms-get" :class="{disabled: smsCountdown>0}" @click="getSmsCode">{{ smsCountdown>0 ? smsCountdown+'s后重发' : '获取验证码' }}</text>
        </view>
        <button class="sms-btn" @click="submitSms">确认</button>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { sendMobile_Can } from '@/api/login';
import { getBankUserList, confirmRepay } from '@/api/common';
import { getUserInfo } from '@/api/members';
import { mapState, mapMutations } from 'vuex';
import storage from '@/utils/storage.js';
export default {
  name: "Cashier",
  data() {
    return {
      amount: 6525, // mock 金额
      autoCancel: 15 * 60, // 15分钟倒计时，单位秒
      payList: [
        { value: "BANK", name: "银行卡" },
        { value: "ALIPAY", name: "支付宝" },
        { value: "WECHAT", name: "微信支付" },
      ],
      selectedPay: "BANK",
      // 银行卡列表
      bankList: [],
      selectedBankCard: null,
      // 还款相关参数
      repayParams: {
        bankcardNo: '',
        isAllRepay: 'N', // Y: 全额还款, N: 部分还款
        loanId: null,
        nper: 1,
        payType: 'DAISHOU',
        repayAmount: 0,
        smsCode: ''
      },
      // 验证码相关
      showSms: false,
      smsCode: '',
      smsCountdown: 0,
      smsTimer: null,
      isGettingSms: false, // 新增，控制验证码获取防抖
    };
  },
  created() {
    this.refreshUserInfo(); // 先刷新用户信息
    this.getBankCardList();
    this.initRepayParams();

  },
  beforeDestroy() {
    clearInterval(this.smsTimer);
  },
  computed: {
    ...mapState(['userInfo']),
    phone() {
      const mobile = this.userInfo.mobile || this.userInfo.phone || '';
      if (!mobile) return '';
      // 脱敏处理
      return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },
    rawPhone() {
      return this.userInfo.mobile || this.userInfo.phone || '';
    },
  },
  methods: {
    ...mapMutations(['login']),
    // 刷新用户信息
    async refreshUserInfo() {
      try {
        const res = await getUserInfo();
        if (res && res.data && res.data.success && res.data.result) {
          // 更新本地存储
          storage.setUserInfo(res.data.result);
          // 更新 Vuex store
          this.login(res.data.result);
          console.log('用户信息已刷新:', res.data.result);
        }
      } catch (error) {
        console.error('刷新用户信息失败:', error);
      }
    },
    // 初始化还款参数
    initRepayParams() {
      // 从页面参数获取相关信息
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options || {};

      // 设置还款参数
      if (options.loanId) {
        this.repayParams.loanId =options.loanId;
      }
      if (options.amount) {
        this.amount = parseFloat(options.amount);
        this.repayParams.repayAmount = this.amount;
      }
      if (options.nper) {
        this.repayParams.nper = parseInt(options.nper);
      }
      if (options.isAllRepay) {
        this.repayParams.isAllRepay = options.isAllRepay;
      }
    },
    // 获取银行卡列表
    async getBankCardList() {
      try {
        const res = await getBankUserList();
        if (res.data.code === 200 && res.data.result && res.data.result.bankInfos) {
          this.bankList = res.data.result.bankInfos;
          // 如果有银行卡，默认选择第一张主卡或第一张卡
          if (this.bankList.length > 0) {
            const mainCard = this.bankList.find(card => card.isMain === 'Y');
            this.selectedBankCard = mainCard || this.bankList[0];
            // 设置还款参数中的银行卡号
            this.repayParams.bankcardNo = this.selectedBankCard.bankcardNumber;
          }
        }
      } catch (error) {
        console.error('获取银行卡列表失败:', error);
        uni.showToast({
          title: '获取银行卡信息失败',
          icon: 'none'
        });
      }
    },
    selectPay(val) {
      this.selectedPay = val;
    },
    onConfirmPay() {
      if (!this.selectedPay) {
        uni.showToast({ title: "请选择支付方式", icon: "none" });
        return;
      }
      this.showSms = true;
    },
    async getSmsCode() {
      if (this.smsCountdown > 0) return;
      
      // 添加防抖机制，防止疯狂点击
      if (this.isGettingSms) return;
      this.isGettingSms = true;
      
      if (!this.rawPhone || !/^1[3-9]\d{9}$/.test(this.rawPhone)) {
        uni.showToast({ title: '手机号无效', icon: 'none' });
        this.isGettingSms = false;
        return;
      }
      try {
        await sendMobile_Can('STAGE_REPAY');
        uni.showToast({ title: '验证码已发送', icon: 'none' });
        this.smsCountdown = 60;
        this.smsTimer = setInterval(() => {
          if (this.smsCountdown > 0) {
            this.smsCountdown--;
          } else {
            clearInterval(this.smsTimer);
          }
        }, 1000);
      } catch (e) {
        uni.showToast({ title: '验证码发送失败', icon: 'none' });
      } finally {
        // 无论成功还是失败，都要重置防抖状态
        this.isGettingSms = false;
      }
    },
    async submitSms() {
      if (!this.smsCode) {
        uni.showToast({ title: '请输入验证码', icon: 'none' });
        return;
      }
      if (!/^\d{6}$/.test(this.smsCode)) {
        uni.showToast({ title: '验证码格式错误', icon: 'none' });
        return;
      }

      // 调用还款接口
      await this.processRepayment();
    },
    // 处理还款
    async processRepayment() {
      try {
        uni.showLoading({ title: '正在处理还款...' });

        // 设置还款参数
        this.repayParams.smsCode = this.smsCode;
        this.repayParams.repayAmount = this.amount;
        // 这里可以根据实际需求设置 loanId，可能需要从页面参数或其他地方获取
        // this.repayParams.loanId = this.getLoanId();

        const res = await confirmRepay(this.repayParams);
        console.log('还款结果:', res);
        
        uni.hideLoading();

        if (res.data.code === 200) {
          // 还款成功
          uni.showToast({ title: '还款成功', icon: 'success' });
          this.showSms = false;
          this.smsCode = '';

          // 跳转到成功页面
          setTimeout(() => {
            // uni.navigateTo({
            //   url: `/pages/financial/repay-success?amount=${this.amount}`
            // });
            uni.switchTab({
              url: '/pages/tabbar/home/<USER>'
            });
          }, 500);
        } else {
          // 还款失败
          uni.showToast({
            title: res.msg || '还款失败，请重试',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.hideLoading();
        console.error('还款处理失败:', error);
        uni.showToast({
          title: '还款处理失败，请重试',
          icon: 'none'
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.cashier-page {
  padding: 20rpx 32rpx 0;
}
.top-card {
  width: 686rpx;
  height: 322rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border-radius: 20rpx;
  padding: 32rpx;
  margin-bottom: 20rpx;
  .top-title {
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    margin-bottom: 20rpx;
    text-align: center;
  }
  .top-amount {
    font-weight: 600;
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 12rpx;
    text-align: center;
    text {
        font-size: 56rpx;
    }
  }
  .top-pay-way {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 40rpx;
    .pay-way-title {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
    }
    .pay-way-item {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
    }
  }
  .pay-tips {
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    margin-top: 12rpx;
  }
  .top-timer {
    font-size: 24rpx;
    color: #999;
  }
}

.pay-btn-box {
    width: 750rpx;
    height: 206rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0,0,0,0.04);
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 32rpx 0;
  .pay-btn-gradient {
    width: 670rpx;
    height: 100rpx;
    background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
    border-radius: 78rpx;
    font-size: 32rpx;
    color: #fff;
    text-align: center;
    line-height: 100rpx;
    border: none;
    margin-bottom: 16rpx;
  }
  .security-tip {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #ff9500;
    justify-content: center;
    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 8rpx;
    }
  }
}
.sms-modal {
  padding: 40rpx;
  background: #fff;
  border-radius: 16rpx;
  min-width: 540rpx;
}
.sms-header {
  position: relative;
  text-align: center;
  margin-bottom: 20rpx;
}
.sms-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.sms-desc {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 26rpx;
  text-align: center;
}
.sms-row {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 40rpx;
}
.sms-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 28rpx;
  color: #222;
  padding: 20rpx 0;
}
.sms-placeholder {
  color: #cccccc;
  font-size: 28rpx;
}
.sms-get {
  color: #ff5134;
  font-size: 28rpx;
  margin-left: 20rpx;
}
.sms-get.disabled {
  color: #ccc;
}
.sms-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(90deg, #FF5235 0%, #FF9500 100%);
  color: #fff;
  font-size: 32rpx;
  border-radius: 50rpx;
  border: none;
  margin-top: 20rpx;
  font-weight: 400;
}
</style>
