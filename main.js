import Vue from "vue";
import App from "./App";
import * as filters from "./utils/filters.js"; // global filter
import uView from "uview-ui";
import store from "./store";
import config from '@/config/config';
// import airBtn from "@/components/m-airbtn/index.vue";
import socketIO from './pages/mine/im/socket';
import ShowModalPlugin from '@/utils/plugin' // 路径按实际情况调整
// import watchPermis from '@/utils/watchPermision.js';
// Vue.use(watchPermis)
// import watchPermision from '@/components/permisonPopup.vue';
// Vue.component('watchPermision', watchPermision);
import { addPermisionInterceptor } from '@/uni_modules/x-perm-apply-instr/js_sdk/index.js'
addPermisionInterceptor('chooseImage', '为了完成身份认证、银行卡绑定、修改个人头像和发布信息等功能，我们需要申请您设备的相机和存储权限')
addPermisionInterceptor('chooseVideo', '为了发布信息图片视频等, 我们需要申请您设备的相机和存储权限')
addPermisionInterceptor('saveImageToPhotosAlbum', '为了保存推广海报到手机相册, 我们需要申请您设备的存储权限')
addPermisionInterceptor('getLocation', '为了根据您的位置展示信息, 我们需要申请您设备的位置权限')
addPermisionInterceptor('makePhoneCall', '为了联系客服/用户/咨询等, 我们需要申请您设备的拨打电话权限')
addPermisionInterceptor('getRecorderManager', '为了使用语言消息功能等, 我们需要申请您设备的麦克风权限')
addPermisionInterceptor('startLocationUpdate', '为了根据您的位置展示信息, 我们需要申请您设备的位置权限')
addPermisionInterceptor('scanCode', '为了识别二维码信息, 我们需要申请您设备的相机权限')
Vue.use(ShowModalPlugin)
/**
 * 仅在h5中显示唤醒app功能
 * 在h5页面手动挂载
 * 
 */
// #ifdef H5
// if (config.enableMiniBarStartUpApp) {
//   let btn = Vue.component("airBtn", airBtn); //全局注册
//   document.body.appendChild(new btn().$mount().$el);
// }
// #endif

// 引入uView对小程序分享的mixin封装
let mpShare = require('uview-ui/libs/mixin/mpShare.js');
Vue.mixin(mpShare)

/**
 * 全局filters
 */

Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});

// 引入Vuex
Vue.prototype.$store = store;
// Vue.prototype.socketIo = new socketIO();
Vue.use(uView);
Vue.config.productionTip = false;


/**
 * 注意！
 * 此处将常用的颜色嵌入到原型链上面
 * 颜色使用驼峰命名对应 uni.scss中全局颜色变量名
 * 如需更换主题请修改此处以及uni.scss中的全局颜色
 */
// 主题色
Vue.prototype.$mainColor = config.mainColor;
// 高亮主题色
Vue.prototype.$lightColor = config.lightColor;
// 辅助高亮颜色
Vue.prototype.$aiderLightColor = config.aiderLightColor;


App.mpType = "app";

const app = new Vue({
  ...App,
});
app.$mount();
