{"name": "TrustDeviceUniPlugin", "id": "TrustDecision-TrustDeviceUniPlugin", "version": "1.2.6", "description": "TrustDevice uni 插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"hooksClass": "cn.tongdun.tdmobrisk.TdMobRiskProxy", "plugins": [{"type": "module", "name": "TrustDecision-TrustDeviceUniPlugin", "class": "cn.tongdun.tdmobrisk.TdMobRiskPlus"}], "integrateType": "aar", "deploymentTarget": "5.0", "minSdkVersion": 21}, "ios": {"plugins": [{"type": "module", "name": "TrustDecision-TrustDeviceUniPlugin", "class": "TrustDeviceModule"}], "frameworks": ["TDMobRisk.xcframework", "FMDeviceManagerFramework.xcframework", "TDCorePlugin.xcframework", "libresolv.9.tbd"], "integrateType": "framework", "resources": ["TDCaptchaResource.bundle"], "deploymentTarget": "9.0"}}}