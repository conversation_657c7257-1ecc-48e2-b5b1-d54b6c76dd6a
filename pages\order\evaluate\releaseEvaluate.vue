<template>
  <view>
    <!-- 遍历出评价商品 -->
    <view>
      <view class="after-sales-goods-detail-view">
        <view>
          <view class="goods-item-view">
            <view class="goods-img">
              <u-image border-radius="8" width="154rpx" height="154rpx" :src="sku.image" />
            </view>
            <view class="goods-info">
              <view class="goods-title ">{{ sku.name }}</view>
              <view class="goods-specs">{{ skuTitle }}</view>
              <view class="goods-price">
                <text class="price">¥{{ Number(sku.goodsPrice).toFixed(2) }}</text>
                <text class="price_num">x{{ sku.num }}</text>
                <!-- <view>x{{ sku.num }}</view> -->
              </view>
            </view>
          </view>
        </view>
      </view>
      <view style="margin-bottom: 20rpx;">
        <view class="info-evaluate-view">
          <view class="input-view">
            <u-input v-model="form.content" height="200" placeholder-style="font-size:12px;color:#CCCCCC" :type="type"
              :border="border" :maxlength="maxlength" :placeholder="placeholder" />
          </view>
          <view class="input-num">
            <text>{{ form.content.length }}/{{ maxlength }}</text>
          </view>
        </view>
        <view class="info-evaluate-view">
          <view class="images-view">
            <u-upload :header=" { accessToken: storage.getAccessToken() }" :action="action" width="150"
              @on-uploaded="onUploaded" :max-count="5" :show-progress="false" :custom-btn="true" :auto-upload="false" ref="uUpload">
              <view slot="addBtn" class="upload-btn">
                <image src="/static/xj.png" mode="scaleToFill" />
                <view class="upload-text">图片</view>
              </view>
            </u-upload>
          </view>
        </view>
      </view>
    </view>
    <view class="info-evaluate-view" style="margin-bottom: 20rpx;padding-top:0;padding-bottom:0;">
      <view>
        <view class="seller-rate-view">
          <view class="rate-title">商品评价</view>
          <view class="rate-content">
            <u-rate count="count" gutter="20" active-color="#FFC71C" v-model="form.descriptionScore" :size="32">
            </u-rate>
          </view>
          <u-icon @click="toggleAllRatings" :name="showAllRatings?'arrow-up':'arrow-down'" color="#999999" size="24"></u-icon>
        </view>
        <view v-show="showAllRatings">
          <view class="seller-rate-view">
            <view class="rate-title">快递包装</view>
            <view class="rate-content">
              <u-rate count="count" gutter="20" active-color="#FFC71C" v-model="form.packageScore" :size="32"></u-rate>
               <view class="rate_box"></view>
            </view>
          </view>
        
          <view class="seller-rate-view">
            <view class="rate-title">送货速度</view>
            <view class="rate-content">
              <u-rate count="count" gutter="20" active-color="#FFC71C" v-model="form.deliveryScore" :size="32">
              </u-rate>
              <view class="rate_box"></view>
            </view>
          </view>
          <view class="seller-rate-view">
            <view class="rate-title">配送员服务</view>
            <view class="rate-content">
              <u-rate count="count" gutter="20" active-color="#FFC71C" v-model="form.serviceScore" :size="32"></u-rate>
              <view class="rate_box"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <u-divider bg-color="#f7f7f7">您的评论内容会被匿名展示</u-divider>
    <!-- <view class="anonymous-tip"></view> -->
    <view class="submit_box">
     <view class="onSubmit" :class="{ 'disabled': isSubmitting }" @click="onSubmit">
       {{ isSubmitting ? '发布中...' : '发布' }}
     </view>
    </view>
    

    <u-toast ref="uToast" />
  </view>
</template>

<script>
import storage from "@/utils/storage.js";
import { commentsMemberOrder } from "@/api/members.js";
import { upload } from "@/api/common.js";

export default {
  data() {
    return {
      showAllRatings: false,
      storage,
      type: "textarea", //输入框状态为 textarea
      border: false, //没有border
      maxlength: 500, //评价最大字数为500字
      placeholder:
        "宝贝满足您的期待吗？说说它的优点和美中不足的地方吧。您的评价会帮助更多的人",
      sku: {}, //订单信息
      form: {
        content: "", //评价详情
        goodsId: "", //商品id
        grade: "GOOD", //默认为好评
        orderItemSn: "", //商品的sn
        skuId: "", //商品skuId
        descriptionScore: 5, //默认描述得分为5分
        serviceScore: 5, //默认服务得分为5分
        deliveryScore: 5, //默认物流得分为5分
        packageScore: 5, //快递包装
        images: [],
      },
      action: upload, //图片上传地址
      skuTitle: "", //商品标题
      count: 5,
      isSubmitting: false, // 防抖标志
    };
  },
  onLoad(options) {
    // 获取上一级传过来的数据进行解析
    this.form.orderItemSn = options.sn;
    this.sku = JSON.parse(decodeURIComponent(options.sku));
    this.skuTitle = options.val;
    this.form.goodsId = this.sku.goodsId;
    this.form.skuId = this.sku.skuId;
  },
  methods: {
    toggleAllRatings() {
      this.showAllRatings = !this.showAllRatings
    },
    /**
     * 点击评价
     */
    onGrade(grade) {
      this.form.grade = grade;
    },
    
    /**
     * 提交评价
     */
     async onSubmit () {
      // 防抖处理，防止疯狂点击
      if (this.isSubmitting) {
        return;
      }
      this.isSubmitting = true;
      
      // 校验评论内容
      if (!this.form.content || this.form.content.trim() === '') {
        this.$refs.uToast.show({
          type: 'warning',
          title: '请填写评论内容',
          duration: 2000
        });
        this.isSubmitting = false; // 重置防抖标志
        return;
      }
  
      try {
        // 判断是否有图片需要上传
        const hasImages = this.$refs.uUpload && this.$refs.uUpload.lists && this.$refs.uUpload.lists.length > 0;
        if (hasImages) {
          // 1. 先上传图片，等图片上传完
          await new Promise((resolve, reject) => {
            this.$refs.uUpload.upload();
            // 监听图片上传完成
            this.$once('upload-finish', resolve);
            // 可选：监听失败
            // this.$once('upload-fail', reject);
          });
        }
        // 2. 图片上传完（或没有图片），this.form.images 已有图片url，提交评价
        const res = await commentsMemberOrder(this.form);
        uni.hideLoading();
        if (res.data.success) {
          uni.showToast({
            title: "发布评价成功",
            duration: 2000,
            icon: "none",
            success: () => {
              setTimeout(() => {
                uni.navigateBack();
              }, 500);
            },
          });
        }
      } catch (e) {
        uni.hideLoading();
        uni.showToast({ title: "图片上传失败", icon: "none" });
      } finally {
        // 无论成功还是失败，都要重置防抖标志
        setTimeout(() => {
          this.isSubmitting = false;
        }, 2000); // 2秒后重置，防止用户快速重复点击
      }
    },

    /**
     * 图片成功后回调
     */
    onUploaded(lists) {
      let images = [];
      lists.forEach((item) => {
        images.push(item.response.result);
      });
      this.form.images = images;
      // 通知上传完成
      this.$emit('upload-finish');
    },
  },
};
</script>

<style lang="scss" scoped>
page,
.content {
  background: $page-color-base;
  height: 100%;
  margin-bottom: 100rpx;
}

.anonymous-tip {
  color: #999;
  font-size: 24rpx;
  text-align: center;
  margin: 40rpx 0;
  position: relative;

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    background: #ccc;
    width: 100rpx;
    height: 1px;
  }

  &::before {
    left: 200rpx;
  }

  &::after {
    right: 200rpx;
  }
}
.submit_box {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: #fff;
}
.onSubmit {
  width: 80%;
  margin: 20rpx auto 20rpx;
  text-align: center;
  color: #fff;
  line-height: 100rpx;
  border-radius: 100px;
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
  border-radius: 86rpx 86rpx 86rpx 86rpx;
  
  &.disabled {
    background: linear-gradient( 90deg, #ccc 0%, #999 100%);
    color: #fff;
    pointer-events: none;
  }
}

.after-sales-goods-detail-view {
  // padding: 10rpx 0rpx;

  .goods-item-view {
    display: flex;
    align-items: center;
    flex-direction: row;
    padding: 20rpx 32rpx;
    background-color: #fff;
    // height: 148rpx;
    background: #FFFFFF;
    margin: 0 auto 20rpx;
    border-top: 1rpx solid rgba(0, 0, 0, 0.1);
    .goods-info {
      padding-left: 20rpx;
      flex: 1;

      .goods-title {
        width: 512rpx;
        margin-bottom: 8rpx;
        font-weight: 400;
        font-size: 32rpx;
        color: #333333;
        // 一行显示超出...
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .goods-specs {
        font-size: 24rpx;
        margin-bottom: 10rpx;
        color: #999;
      }
      .goods-price {
        font-size: 28rpx;
        margin-bottom: 10rpx;
        color: $light-color;
        .price {
          font-weight: 500;
          font-size: 28rpx;
          color: #FF5134;
        }
        .price_num { 
          font-weight: 400;
          font-size: 20rpx;
          color: #999999;
        }
      }
    }
  }
}

.info-evaluate-view {
  padding: 20rpx;
  background-color: #fff;
  align-items: center;
  font-size: 24rpx;

  .input-view {
    width: 100%;
  }

  .input-num {
    color: #cccccc;
    text-align: right;
  }

  .images-view {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: wrap;
  }
}

.seller-rate-view {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  // border-bottom: 1px solid #f2f2f2;
  align-items: center;
  font-size: 28rpx;

  &:last-child {
    border: none;
  }

  .rate-title {
    color: #333333;
  }

  .rate-content {
    flex: 1;
    padding: 0 20rpx;
    display: flex;
    justify-content: flex-end;
  }
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  background: #F7F7F7;
  border-radius: 12rpx;

  image {
    width: 68rpx;
    height: 68rpx;
  }
}

.upload-text {
  font-weight: 400;
  font-size: 20rpx;
  color: #666666;
  margin-top: 12rpx;
}
.rate_box {
  width: 12rpx;
  height: 12rpx;
  margin-left: 10rpx;
}
</style>
