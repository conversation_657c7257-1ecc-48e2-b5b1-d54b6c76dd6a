//
//  FMDeviceManager.h
//  FMDeviceManager
//
//  
//

#define FM_SDK_VERSION @"4.3.0.1"

#import <Foundation/Foundation.h>

typedef void(^TDGetBlackBoxAsyncBlock)(NSString* blackBox);

typedef void(^TDErrorCodeBlock)(int errorCode, const char*errorMsg);

typedef struct FMDeviceManager_Void {
    
    void (*initWithOptions)(NSDictionary *);
    
    NSString *(*getBlackBox)(void);
    
    // manager->getBlackBoxAsync(^(NSString* blackBox){});
    void (*getBlackBoxAsync)(TDGetBlackBoxAsyncBlock block);
    
    // manager->setOnErrorCodeListener(^(int errorCode,const char* errorMsg){})
    void (*setOnErrorCodeListener)(TDErrorCodeBlock block);
    
    NSString *(*getInitStatus)(void);
    
    NSDictionary *(*getConfigInfo)(void);
    
} FMDeviceManager_t;

#ifdef SDKTYPE
#ifdef SAAS
@interface FMDeviceManager_SAAS : NSObject
#elif defined(ENTERPRISE)
@interface FMDeviceManager_ENTERPRISE : NSObject
#else
#error "error SDKTYPE"
#endif
#else
@interface FMDeviceManager : NSObject
#endif

+ (FMDeviceManager_t *) sharedManager;

@end

