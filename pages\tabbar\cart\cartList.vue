<template>
  <div class="wrapper">
    
    <u-navbar :is-back="false" title="购物车">
      <!-- <div slot="right">
        <div class="light-color edit" @click="isEdit = !isEdit">{{ !isEdit ? '编辑' : '完成'}}</div>
      </div> -->
    </u-navbar> 
    <!-- 空白页-->
    <view v-if="dataReady && (!cartDetail || !cartDetail.cartList || cartDetail.cartList.length === 0)" class="empty">
      <image src="/static/emptyCart.png" mode="aspectFit"></image>
      <view class="empty-tips">
        空空如也
        <navigator class="navigator" url="/pages/tabbar/home/<USER>" open-type="switchTab">随便逛逛></navigator>
      </view>
    </view>
    <!-- 店铺商品信息 -->
    <div class="content" v-if="loading || (dataReady && flattenedCartItems.length > 0)">
      <!-- 调试信息 -->
      <!-- <div style="padding: 20rpx; background: #f0f0f0; margin: 20rpx; border-radius: 10rpx;">
        <div>dataReady: {{ dataReady }}</div>
        <div>cartDetail: {{ cartDetail ? '有数据' : '无数据' }}</div>
        <div>flattenedCartItems.length: {{ flattenedCartItems.length }}</div>
        <div>loading: {{ loading }}</div>
      </div> -->

      <!-- 加载中提示 -->
      <div v-if="loading" class="loading-container">
        <view class="loading-text">加载中...</view>
      </div>

      <div class="cart-list" v-if="dataReady && flattenedCartItems.length > 0">
        <div
          class="cart-item-wrapper"
          v-for="(skuItem, index) in flattenedCartItems"
          :key="(skuItem.goodsSku && skuItem.goodsSku.id) ? skuItem.goodsSku.id + '-' + index : 'empty-' + index"
        >
          <u-swipe-action
            :ref="`swiperAction_${index}`"
            :options="options"
            :show="swipeStates[index] || false"
            :index="index"
            @click="changeActionTab(skuItem, index)"
          >
            <div class="cart-item" v-if="skuItem && skuItem.goodsSku && skuItem.goodsSku.id">
              <div class="img-container">
                  <image
                    class="item-img"
                    :src="skuItem.goodsSku.thumbnail || '/static/nodata.png'"
                    @click="navigateToGoods(skuItem)"
                    mode="aspectFill"
                    @error="handleImageError"
                  />
              </div>
              <div class="item-info title-wrap">
                <div class="item-header">
                  <div class="item-title">{{ skuItem.goodsSku.goodsName || '商品名称加载中...' }}</div>
                  <uni-number-box
                    class="item-num"
                    :min="1"
                    :max="999"
                    v-model="skuItem.num"
                    @change="numChange(skuItem)"
                  />
                </div>
                <div class="item-spec">{{ skuItem.goodsSku.simpleSpecs || '规格信息加载中...' }}</div>
                <div class="item-footer">
                  <div class="item-price">￥<span class="item-price-main">{{ skuItem.subTotal || '0.00' }}</span></div>
                  <view class="buy-btn" @click="buyNow(skuItem)">立即购买</view>
                </div>
              </div>
            </div>
          </u-swipe-action>
        </div>
      </div>
    </div>
    <u-modal v-model="deleteShow" :confirm-style="{'color':lightColor}" @confirm="deleteConfirm" show-cancel-button
      :content="deleteContent" :async-close="true"></u-modal>
    <!-- 结账 -->
    <!-- 判断是否有商品来显示结算按钮 v-if="cartDetail && cartDetail.cartList && cartDetail.cartList.length != 0"-->
    <div class="box box6" v-if="false">
      <view class="navL">
        <u-checkbox shape="circle" :active-color="lightColor" v-model="checkout" @change="checkOut()" label-size="24">全选
        </u-checkbox>
        <span class="price">
          <div class="prices">
            <div class="fullPrice">
              <span class="number" v-if="cartDetail && cartDetail.priceDetailDTO">
                总计:
                <span>¥{{ $options.filters.goodsFormatPrice(cartDetail.priceDetailDTO.flowPrice)[0] }}</span>.<span>{{ $options.filters.goodsFormatPrice(cartDetail.priceDetailDTO.flowPrice)[1] }}</span>
              </span>
              <span class="number" v-else>总计:0.00</span>
            </div>
            <div
              v-if="cartDetail.cartList && cartDetail.cartList.length!=0 && cartDetail.priceDetailDTO && cartDetail.priceDetailDTO.discountPrice!=0 "
              class="discountPrice">
              <span>优惠减:￥{{(cartDetail.priceDetailDTO.goodsPrice - cartDetail.priceDetailDTO.flowPrice) | unitPrice}}
              </span>
              <span class="discount-details" @click="discountDetails">优惠明细</span>
            </div>
          </div>
        </span>
      </view>
      <!-- 优惠详情 -->
      <u-popup z-index="3" close mode="bottom" height="50%" closeable v-model="discountDetailsFlag" border-radius="20">
        <div class="discount-list">
          <view class="discount-title">优惠明细</view>
          <div class="discount-way">
            <div class="discount-item" v-if="cartDetail.priceDetailDTO">
              <span>商品总额</span>
              <span>￥{{cartDetail.priceDetailDTO.goodsPrice | unitPrice}}</span>

            </div>
            <div class="discount-item" v-if="cartDetail.priceDetailDTO">
              <span>优惠券</span>
              <span>-￥{{cartDetail.priceDetailDTO.couponPrice | unitPrice}}</span>
            </div>
            <div class="discount-item" v-if="cartDetail.priceDetailDTO">
              <span>其他优惠</span>
              <span>-￥{{cartDetail.priceDetailDTO.discountPrice | unitPrice}}</span>
            </div>
          </div>
        </div>
      </u-popup>

      <view v-if="isEdit" @click="deleteGoods()">
        <div class="settlement">删除</div>
      </view>

      <view v-else @click="submitOrder()">
        <div class="settlement">去结算</div>
      </view>
    </div>
    <u-toast ref="uToast" />
    <Guess
      v-if="cartListT && cartListT.length > 0 && dataReady"
      type="cart"
      :title="cartTitle || '为您精选'"
      ref="guessComponent"
      :goodsList="cartListT"
      :show="false"
    />
  </div>
</template>
<script>
import * as API_Trade from "@/api/trade";
import { debounce } from "@/utils/tools.js";
import uniNumberBox from '@/components/uni-number-box'
import Guess from '@/components/Guess.vue';
import { getHomeData } from "@/api/home";
export default {
  components:{uniNumberBox,Guess}, // 数量加减组件
  data() {
    return {
      loading:false,
      dataReady: false, // 数据是否准备就绪
      cartDataReady: false, // 购物车数据是否准备就绪
      recommendDataReady: false, // 推荐商品数据是否准备就绪
      isLoadingData: false, // 是否正在加载数据，防止重复请求
      swipeStates: {}, // 滑动状态管理
      lightColor: this.$lightColor,
      discountDetailsFlag: false, //优惠明细开关
      // 商品栏右侧滑动按钮
      options: [
        {
          text: "删除",
          style: {
            backgroundColor: this.$lightColor, //高亮颜色
          },
        },
      ],
      isInvalid(val) {
        //是否无效商品/没库存商品
        if (val.invalid == 1 || (!val.checked && val.errorMessage)) {
          return true;
        } else {
          return false;
        }
      },
      deleteShow: false, //右滑删除
      deleteContent: "删除该商品？", //删除显示的信息
      cartDetail: { cartList: [] }, //购物车详情
      goodsVal: "", //单个商品详情
      isEdit: false, // 是否是编辑
      checkout: false, //全选按钮
      WEIXIN_num: "", //购物车兼容微信步进器
      cartListT: [],
      cartTitle:'',
      pageParams: {
        page: 1,
        pageSize: 10,
      }
    };
  },
  onLoad() {
    // 初始化购物车数据，确保数据加载完成后再进行其他操作
    this.$nextTick(() => {
      // 数据加载将在onShow中进行
    });
  },
  computed: {
    flattenedCartItems() {
      console.log('computed flattenedCartItems 被调用, dataReady:', this.dataReady);
      console.log('cartDetail:', this.cartDetail);

      if (!this.cartDetail || !this.cartDetail.cartList) {
        console.log('购物车数据为空:', this.cartDetail);
        return [];
      }

      try {
        const items = this.cartDetail.cartList.reduce((acc, store) => {
          if (store && store.skuList && Array.isArray(store.skuList)) {
            console.log('处理店铺:', store.storeName, '商品数量:', store.skuList.length);
            // 为每个商品添加店铺信息，并过滤掉无效数据
            const validItems = store.skuList.filter(sku => {
              // 更严格的数据验证
              const isValid = sku &&
                     sku.goodsSku &&
                     sku.goodsSku.id;
              if (!isValid) {
                console.log('无效商品数据:', sku);
              }
              return isValid;
            }).map(sku => ({
              ...sku,
              storeName: store.storeName || '未知店铺',
              storeId: store.storeId || '',
              // 确保必要字段存在
              goodsSku: {
                ...sku.goodsSku,
                goodsName: sku.goodsSku.goodsName || '商品名称加载中...',
                thumbnail: sku.goodsSku.thumbnail || '',
                simpleSpecs: sku.goodsSku.simpleSpecs || '规格信息加载中...'
              },
              num: sku.num || 1,
              subTotal: sku.subTotal || '0.00',
              // 确保selected字段始终为false，防止删除按钮意外显示
              selected: false
            }));
            return [...acc, ...validItems];
          }
          return acc;
        }, []);

        console.log('处理后的购物车商品数量:', items.length);
        console.log('处理后的购物车商品:', items);
        return items;
      } catch (error) {
        console.error('处理购物车数据时出错:', error);
        return [];
      }
    }
  },

  mounted() {
    // #ifdef MP-WEIXIN
    // 小程序默认分享
    uni.showShareMenu({ withShareTicket: true });
    // #endif

    // 监听添加购物车成功事件
    uni.$on('cartItemAdded', this.handleCartItemAdded);

    // 监听登录状态变化事件
    uni.$on('loginStatusChanged', this.handleLoginStatusChanged);
  },
  beforeDestroy() {
    // 移除事件监听
    uni.$off('cartItemAdded', this.handleCartItemAdded);
    uni.$off('loginStatusChanged', this.handleLoginStatusChanged);
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新购物车');
    this.forceRefreshCart();
  },
  /**
   * 初始化信息
   */
  onShow() {
    this.deleteShow ? (this.deleteShow = false) : true;

    // 重置数据状态
    this.dataReady = false;
    this.loading = true;

    // 并行加载购物车数据和推荐商品数据
    this.loadAllData();
  },
  methods: {
    /**
     * 并行加载所有数据
     */
    async loadAllData() {
      // 防止重复请求
      if (this.isLoadingData) {
        console.log('数据正在加载中，跳过重复请求');
        return;
      }
      
      this.isLoadingData = true;
      
      try {
        // 重置状态
        this.cartDataReady = false;
        this.recommendDataReady = false;
        
        // 并行执行两个数据加载任务
        const [cartResult, recommendResult] = await Promise.allSettled([
          this.getCardData(),
          this.getCardDataList()
        ]);

        // 处理购物车数据结果
        if (cartResult.status === 'fulfilled') {
          console.log('购物车数据加载成功');
          this.cartDataReady = true;
        } else {
          console.error('购物车数据加载失败:', cartResult.reason);
          this.cartDataReady = true; // 即使失败也标记为完成
        }

        // 处理推荐商品数据结果
        if (recommendResult.status === 'fulfilled') {
          console.log('推荐商品数据加载成功');
          this.recommendDataReady = true;
        } else {
          console.error('推荐商品数据加载失败:', recommendResult.reason);
          this.recommendDataReady = true; // 即使失败也标记为完成
        }

        // 只有当两个数据都加载完成后才设置数据准备就绪
        if (this.cartDataReady && this.recommendDataReady) {
          this.dataReady = true;
          this.loading = false;
          console.log('所有数据加载完成，显示内容');
        }
        
        // 强制触发视图更新
        this.$forceUpdate();
        
      } catch (error) {
        console.error('数据加载过程中发生错误:', error);
        this.dataReady = true;
        this.loading = false;
        this.cartDataReady = true;
        this.recommendDataReady = true;
      } finally {
        // 重置加载状态
        this.isLoadingData = false;
      }
    },

    // 重置所有滑动组件状态
    resetAllSwipeActions() {
      try {
        // 重置所有商品的selected状态
        if (this.cartDetail && this.cartDetail.cartList) {
          this.cartDetail.cartList.forEach(store => {
            if (store.skuList) {
              store.skuList.forEach(sku => {
                this.$set(sku, 'selected', false);
              });
            }
          });
        }

        // 重置滑动组件
        this.$nextTick(() => {
          // 遍历所有可能的滑动组件ref
          Object.keys(this.$refs).forEach(refKey => {
            if (refKey.startsWith('swiperAction_')) {
              const component = this.$refs[refKey];
              if (component && typeof component.close === 'function') {
                component.close();
              }
            }
          });
        });
      } catch (error) {
        console.error('重置滑动组件状态失败:', error);
      }
    },

    // 关闭所有滑动组件
    closeAllSwipeActions() {
      try {
        this.$nextTick(() => {
          // 遍历所有可能的滑动组件ref
          Object.keys(this.$refs).forEach(refKey => {
            if (refKey.startsWith('swiperAction_')) {
              const component = this.$refs[refKey];
              if (component && typeof component.close === 'function') {
                component.close();
              }
            }
          });
        });
      } catch (error) {
        console.error('关闭滑动组件失败:', error);
      }
    },

    // 重置滑动状态
    resetSwipeStates() {
      this.swipeStates = {};
      this.$forceUpdate();
    },

    // 初始化滑动状态
    initSwipeStates() {
      const states = {};
      this.flattenedCartItems.forEach((item, index) => {
        states[index] = false;
      });
      this.swipeStates = states;
    },

    async getCardDataList () {
      try {
        const res = await getHomeData(this.pageParams)
        if (res && res.data && res.data.result && res.data.result.pageData) {
          const result = JSON.parse(res.data.result.pageData)
          console.log('推荐商品数据:', result);
          
          if (result && result.list && result.list.length > 0 && 
              result.list[0].options && result.list[0].options.list && 
              result.list[0].options.list.length > 0) {
            
            this.cartTitle = result.list[0].options.list[0].titleWay[0].title || '为您精选'
            this.cartListT = result.list[0].options.list[0].listWay || []
            console.log('推荐商品列表:', this.cartListT);
          } else {
            console.warn('推荐商品数据结构异常:', result);
            this.cartTitle = '为您精选'
            this.cartListT = []
          }
        } else {
          console.warn('获取推荐商品数据失败:', res);
          this.cartTitle = '为您精选'
          this.cartListT = []
        }
      } catch (error) {
        console.error('获取推荐商品数据异常:', error);
        this.cartTitle = '为您精选'
        this.cartListT = []
      }
    },
    /**
			 * 倒数计时
			 */
    getCountDownTime(val) {
      if (val.promotionMap) {
        let key = Object.keys(val.promotionMap).find((child, index) => {
          return child.split("-")[0] == 'SECKILL'
        });
        return val.promotionMap[key].endTime / 1000 - (new Date().getTime() / 1000)
      }
    },

    /**
     * 优惠明细开关
     */
    discountDetails() {
      this.discountDetailsFlag = true;
    },
    /**
     * 左滑打开删除
     */
    openAction(skuItem) {
      /**循环父级有多少个店铺 */
      this.cartDetail.cartList.forEach((cartItem) => {
        if (cartItem.skuList) {
          cartItem.skuList.forEach((sku) => {
            this.$set(sku, "selected", false);
          });
        }
      });
      this.$set(skuItem, "selected", true);
    },

    /**
     * 滑动删除
     */
    changeActionTab(val, index) {
      try {
        if (!val || !val.goodsSku || !val.goodsSku.id) {
          console.warn('滑动删除：商品数据无效', val);
          return;
        }

        console.log('滑动删除事件触发', val.goodsSku.id);

        // 先关闭其他所有滑动组件
        this.resetSwipeStates();

        this.deleteShow = true;
        this.goodsVal = val;
      } catch (error) {
        console.error('滑动删除处理失败:', error);
      }
    },

    /**
     * 点击删除
     */
    deleteConfirm() {
      API_Trade.deleteSkuItem(this.goodsVal.goodsSku.id).then((res) => {
        if (res.statusCode == 200) {
          uni.showToast({
            title: "此商品删除成功",
            duration: 2000,
          });
          this.deleteShow = false;

          // 重置滑动组件状态
          this.resetAllSwipeActions();

          // 重新获取购物车数据
          this.getCardData();
        }
      }).catch((error) => {
        console.error('删除商品失败:', error);
        this.deleteShow = false;
        this.resetAllSwipeActions();
      });
    },

    /**
     * 删除商品
     */
    deleteGoods() {
      if (this.whetherChecked()) {
        var delGoodsData = [];
        this.cartDetail.cartList.forEach((item) => {
          item.skuList.forEach((goodsItem) => {
            if (goodsItem.checked) {
              delGoodsData.push(goodsItem.goodsSku.id);
            }
          });
        });
        if (delGoodsData && delGoodsData.length > 0) {
          // 执行删除
          API_Trade.deleteSkuItem(delGoodsData).then((res) => {
            if (res.data.success) {
              uni.showToast({
                title: "删除成功!",
                icon: "none",
              });
              this.getCardData();
            }
          });
        } else {
          uni.showToast({
            title: "请选择删除商品，如果商品失效，请左滑无效商品删除",
            icon: "none",
          });
        }
      }
    },

    /**
     * 跳转到店铺
     */
    navigateToStore(val) {
      uni.navigateTo({
        url: "/pages/product/shopPage?id=" + val.storeId,
      });
    },

    /**
     * 跳转到优惠券
     */
    navigateToCoupon(val) {
      uni.navigateTo({
        url: "/pages/cart/coupon/couponCenter?storeId=" + val.storeId,
      });
    },

    /**
     * 跳转到商品
     */
    navigateToGoods(val) {
      uni.navigateTo({
        url:
          "/pages/product/goods?id=" +
          val.goodsSku.id +
          "&goodsId=" +
          val.goodsSku.goodsId,
      });
    },

    /**
     * 点击步进器回调
     */
     numChange: debounce(function (val) {
      this.updateSkuNumFun(val.goodsSku.id, val.num);
    }, 1000),
    /**
     * 去结算
     */
    submitOrder() {
      if (this.whetherChecked()) {
        this.navigateTo("/pages/order/fillorder?way=CART");
      }
    },

    /**
     * 验证是否选中商品
     */
    whetherChecked() {
    this.$options.filters.forceLogin()

      let canBuy = false;
      this.cartDetail.cartList.forEach((item) => {
        if (item.checked) {
          canBuy = true;
        } else {
          item.skuList.forEach((skuItem) => {
            if (skuItem.checked) {
              canBuy = true;
            }
          });
        }
      });
      if (!canBuy) {
        uni.showToast({
          title: "您还没有选择商品",
          duration: 2000,
          icon: "none",
        });
        return false;
      } else {
        return true;
      }
    },

    /**
     * 跳转
     */
    navigateTo(url) {
      uni.navigateTo({
        url,
      });
    },

    /**
     * 全选
     */
    checkOut() {
      API_Trade.checkAll(this.checkout).then((result) => {
        if (result.data.success) {
          this.getCardData();
          return true;
        }
      });
    },

    /**
     * 获取店铺选中信息
     */
    checkStoreFun(skuId, num) {
      API_Trade.checkStore(skuId, num).then((result) => {
        if (result.data.success) {
          this.getCardData();
        }
      });
    },

    /**
     * 店铺点击
     */
    checkboxChangeDP(e) {
      // #ifdef MP-WEIXIN
      e.checked = !e.checked;
      // #endif
      this.checkStoreFun(e.storeId, e.checked);
    },

    /**
     * 获取购物车选中信息
     */
    updateSkuCheckedFun(skuId, num) {
      API_Trade.updateSkuChecked(skuId, num).then((result) => {
        if (result.data.success) {
          this.getCardData();
        }
      });
    },

    /**
     * 更新商品购物车数量
     */
    updateSkuNumFun(skuId, num) {
      API_Trade.updateSkuNum(skuId, num).then((result) => {
        if (result.statusCode == 200) {
          this.getCardData();
        } else {
          let _this = this;
          setTimeout(() => {
            _this.getCardData();
          }, 1);
        }
      });
    },

    // 数据去重一下
    getPromotion(item) {
        return Object.keys(item.promotionMap).map((child) => {
          return child.split("-")[0]
        });
    },

    /**
     * 获取购物车数据
     */
    getCardData() {
      return new Promise((resolve, reject) => {
        if (this.$options.filters.isLogin("auth")) {
          API_Trade.getCarts()
          .then((result) => {
            this.loading = false;
            uni.stopPullDownRefresh();
            console.log('购物车接口返回数据:', result);

            if (result && result.data && result.data.success) {
              // 验证数据结构
              const cartData = result.data.result;
              if (cartData && typeof cartData === 'object') {
                // 确保 cartList 是数组
                if (!cartData.cartList) {
                  cartData.cartList = [];
                }
                if (!Array.isArray(cartData.cartList)) {
                  console.warn('cartList 不是数组，重置为空数组');
                  cartData.cartList = [];
                }

                // 深度验证和修复商品数据
                if (cartData.cartList && cartData.cartList.length > 0) {
                  cartData.cartList.forEach(store => {
                    if (store && store.skuList && Array.isArray(store.skuList)) {
                      store.skuList.forEach(sku => {
                        // 确保商品基本信息完整
                        if (sku && sku.goodsSku) {
                          if (!sku.goodsSku.goodsName) {
                            sku.goodsSku.goodsName = '商品名称加载中...';
                          }
                          if (!sku.goodsSku.thumbnail) {
                            sku.goodsSku.thumbnail = '';
                          }
                          if (!sku.goodsSku.simpleSpecs) {
                            sku.goodsSku.simpleSpecs = '规格信息加载中...';
                          }
                          if (!sku.num) {
                            sku.num = 1;
                          }
                          if (!sku.subTotal) {
                            sku.subTotal = '0.00';
                          }
                        }
                      });
                    }
                  });
                }

                this.cartDetail = cartData;
                console.log('设置购物车数据:', this.cartDetail);
                console.log('cartList长度:', cartData.cartList ? cartData.cartList.length : 0);

                // 初始化滑动状态
                this.$nextTick(() => {
                  this.initSwipeStates();
                });

                // 强制触发视图更新
                this.$forceUpdate();
                console.log('调用$forceUpdate');
              } else {
                console.error('购物车数据格式异常:', cartData);
                this.cartDetail = { cartList: [] };
              }
              let checkOuted = true;
              for (let i = 0; i < this.cartDetail.cartList.length; i++) {
                let item = this.cartDetail.cartList[i];
                console.log(item);
                // 循环出当前商品是否全选
                if (item.checked == 0) {
                  checkOuted = false;
                }
                // 如果有拼团活动顺便删除
                item.skuList &&
                  item.skuList.forEach((sku) => {
                    if (sku.checked == 0) {
                      checkOuted = false;
                    }
                    if(Object.keys(sku.promotionMap).length != 0)
                    {
                      Object.keys(sku.promotionMap).forEach((pro, proIndex) => {
                        pro = pro.split('-')[0]
                        if (pro == "PINTUAN" ) {
                           Object.keys(sku.promotionMap).splice(proIndex, 1);
                        }
                      });
                    }

                  });
              }
              this.checkout = checkOuted;
              uni.stopPullDownRefresh();
              // 清空所有selected状态
              if (this.cartDetail && this.cartDetail.cartList) {
                this.cartDetail.cartList.forEach(store => {
                  if (store.skuList) {
                    store.skuList.forEach(sku => {
                      this.$set(sku, 'selected', false);
                    });
                  }
                });
              }
              this.$nextTick(() => {
                // 使用新的重置方法
                this.resetAllSwipeActions();
              });
              resolve(); // 数据加载成功
            }
          })
          .catch((err) => {
            console.error('获取购物车数据失败:', err);
            this.loading = false;
            // 确保有默认数据结构
            if (!this.cartDetail) {
              this.cartDetail = { cartList: [] };
            }
            uni.showToast({
              title: '购物车加载失败，请重试',
              icon: 'none'
            });
            reject(err); // 数据加载失败
          });
        } else {
          // 用户未登录，清空购物车数据
          console.log('用户未登录，清空购物车数据');
          this.loading = false;
          this.cartDetail = { cartList: [] };
          this.checkout = false;
          this.resetSwipeStates(); // 重置滑动状态
          uni.stopPullDownRefresh();
          resolve(); // 清空数据完成
        }
      });
    },

    /**
     *  选中某个复选框时，由checkbox时触发
     */
    checkboxChange(e) {
      // #ifdef MP-WEIXIN
      e.checked = !e.checked;
      // #endif
      this.updateSkuCheckedFun(e.goodsSku.id, e.checked);
    },

    buyNow(skuItem) {
      console.log(skuItem,'BUY_NOW');
      // 构建正确的数据结构
      let data = {
        skuId: skuItem.goodsSku.id,
        num: skuItem.num,
        cartType: 'BUY_NOW' // 明确指定购买类型
      };
      
      API_Trade.addToCart(data).then(res => {
        if (res.data.success) {
          uni.navigateTo({
            url: `/pages/order/fillorder?way=${data.cartType}&addr=${''}&parentOrder=${encodeURIComponent(JSON.stringify(''))}`
          });
        }
      });
    },
    /**
    * 触底事件
    */
    onReachBottom() {
      console.log("触底事件");
      // 直接调用Guess组件的getData方法
      if (this.$refs.guessComponent && typeof this.$refs.guessComponent.getData === 'function') {
        this.$refs.guessComponent.getData();
      }
    },
    /**
     * 处理购物车添加成功事件
     */
    handleCartItemAdded(data) {
      console.log('购物车添加成功，刷新购物车数据', data);
      // 刷新购物车数据
      this.getCardData();
      // 可以在这里添加一些额外的提示或动画效果

    },

    /**
     * 处理图片加载错误
     */
    handleImageError(e) {
      console.log('图片加载失败:', e);
      // 可以设置默认图片或者其他处理逻辑
    },



    /**
     * 强制刷新购物车数据
     */
    forceRefreshCart() {
      console.log('强制刷新购物车数据');
      // 重置数据状态
      this.dataReady = false;
      this.loading = true;
      this.cartDetail = { cartList: [] };
      
      // 使用并行加载方式刷新数据
      this.loadAllData();
    },

    /**
     * 处理登录状态变化
     */
    handleLoginStatusChanged(isLoggedIn) {
      console.log('登录状态变化:', isLoggedIn);
      if (!isLoggedIn) {
        // 用户退出登录，立即清空购物车数据
        console.log('用户退出登录，清空购物车数据');
        this.dataReady = false; // 重置数据状态
        this.cartDataReady = false;
        this.recommendDataReady = false;
        this.cartDetail = { cartList: [] };
        this.checkout = false;
        this.loading = false;
      } else {
        // 用户登录，重新获取购物车数据
        console.log('用户登录，重新获取购物车数据');
        this.dataReady = false; // 重置数据状态
        this.cartDataReady = false;
        this.recommendDataReady = false;
        this.loading = true;
        // 使用并行加载方式获取数据
        this.loadAllData();
      }
    },
  },
};
</script>

<style lang="scss">
page {
  background: #f2f2f2;
}
</style>
<style scoped lang="scss">
// #ifdef MP-WEIXIN
@import "./mp-carui.scss";
// #endif
.u-image {
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.05);
}
.edit{
  padding-right: 32rpx;
  font-size: 28rpx;
}
.img-container {
  position: relative;
  width: 164rpx;
  height: 164rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  border-radius: 16rpx;
  overflow: hidden;
}


.promotion-notice {
  margin-top: 10px;
  margin-left: 68rpx;
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  display: flex;
  align-items: center;
  /deep/ .tips {
    margin: 0 8rpx 0 0;
    background: $main-color;
    border-radius: 100px;
    display: block;
    flex: 1;
    padding: 2rpx 12rpx;
    color: #fff;
  }
}
.default-color {
  color: #333;
}
.goods-row {
  padding: 30rpx 0;

  display: flex;
  align-items: center;
}

.store-name {
  font-weight: bold;
  font-size: 28rpx;
}
.invalid {
  filter: grayscale(1);
}

.cart-item {
  width: 686rpx;
  height: 228rpx;
  padding: 32rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  display: flex;
  align-items: flex-start;
  box-shadow: 0 4rpx 16rpx 0 rgba(255, 126, 0, 0.04);
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}
.item-img {
  width: 164rpx;
  height: 164rpx;
  border-radius: 16rpx;
  object-fit: cover;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.item-info {
  flex: 1;
  min-width: 0;
  justify-content: space-between;
  position: relative;
}
.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #222;
  max-width: 240rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}
.item-num {
  margin: 0;
  /deep/ .uni-number-box {
    height: 52rpx !important;
    min-width: 120rpx;
    border-radius: 26rpx;
    border: 1rpx solid #DEDEDE;
    background: #F6F6F6;
    overflow: hidden;
  }
  /deep/ .uni-number-input {
    background: transparent !important;
    border: none !important;
    font-size: 28rpx !important;
    color: #333333;
    font-weight: bold;
  }
  /deep/ .uni-number-box__minus, /deep/ .uni-number-box__plus {
    background: #FFFFFF !important;
    border: none !important;
    color: #333333 !important;
    font-size: 28rpx;
    width: 52rpx !important;
    height: 52rpx !important;
    line-height: 52rpx !important;
  }
}
.item-spec {
  font-size: 24rpx;
  color: #999999;
  margin: 6rpx 0 0 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  text-align: left;
  height: 36rpx;
  margin-bottom: 10rpx;
}
.item-footer {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  margin-top: auto;
}
.item-price {
  color: #ff4d4f;
  font-size: 28rpx;
  font-weight: bold;
  min-width: 120rpx;
  line-height: 1.2;
}
.item-price-main {
  font-size: 28rpx;
  font-weight: bold;
}
.buy-btn {
  background: linear-gradient(90deg, #ff5000 0%, #ff7e00 100%);
  color: #fff;
  border-radius: 34rpx;
  padding: 0 44rpx;
  height: 68rpx;
  line-height: 68rpx;
  font-size: 28rpx;
  // font-weight: bold;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(255,126,0,0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  margin:  0;
}



.cart-item-wrapper {
  margin-top: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

/deep/ .u-swipe-action {
  border-radius: 20rpx;
  overflow: hidden;
}

/deep/ .u-swipe-view {
  border-radius: 20rpx;
  overflow: hidden;
}

/deep/ .u-swipe-content {
  border-radius: 20rpx;
  overflow: hidden;
}

/* 空白页 */
/deep/ .u-number-input {
  background: #fff !important;
  border: 1px solid #ededed;
  margin: 0 !important;
}

/deep/ .u-icon-minus,
/deep/ .u-icon-plus {
  background: #ffffff !important;
  border: 1px solid #ededed;
  color: #333 !important;
  width: 40rpx;
}

.empty {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 30vh;
  z-index: 99;
  padding-top: 20rpx;
  padding-bottom: var(--window-bottom);
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  background: #fff;
  box-shadow: 0 4rpx 16rpx 0 rgba(255, 126, 0, 0.04);
  position: relative;
  box-sizing: border-box;
  overflow: hidden;

  image {
    width: 240rpx;
    height: 160rpx;
    margin-bottom: 30rpx;
  }

  .empty-tips {
    display: flex;
    font-size: $font-sm + 2rpx;
    color: $font-color-disabled;

    .navigator {
      color: $uni-color-primary;
      margin-left: 16rpx;
    }
  }
}

.settlement {
  width: 180rpx;
  height: 70rpx;
  line-height: 70rpx;
  background: linear-gradient(91deg, $light-color 1%, $aider-light-color 99%);
  border-radius: 900rpx;
  text-align: center;
  color: #fff;
  margin-right: 10rpx;
}

.price {
  display: flex;
  align-items: center;

  /deep/ .number {
    line-height: 1;
    font-size: 30rpx;
    > span {
      font-weight: bold;
    }
  }
}

.box2 {
  border-radius: 20rpx;
  padding: 0 16rpx 0;
  margin: 0 16rpx 20rpx;
  .u-checkbox {
    display: flex;
    align-items: center;
    text-align: center;
  }
  background: #fff;

}

.wrapper {
  height: 100%;
}

/deep/ .u-col {
  padding: 24rpx 0 !important;
}

.goods-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
  > p {
    padding-left: 20rpx;
  }
}

.content {
  // padding: 20rpx 0 20rpx 0;
  // margin-bottom: 80rpx;
}

.line {
  float: left;
  width: 1px;
  height: 100%;
  border-right: 1px solid $light-color;
}

.store-line-check,
.store-line-img,
.store-line-desc {
  // #ifdef MP-WEIXIN
  float: left;
  // #endif
}

.store-line {
  // #ifndef MP-WEIXIN
  display: flex;
  // #endif
  overflow: hidden;
  flex:10;
}

.goods-config {
  display: flex;
  align-items: center;
  /deep/ .invalid {
    display: block;
    width: 80rpx !important;
  }
}
.tab {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0 0 0;
}

.right-col {
  flex:2;
  text-align: center;
  width: 100rpx;
  color: $light-color;
  font-size: 26rpx;

  > span {
    margin-left: 20rpx;
  }
}

.right-line {
  width: 3px;
  float: left;
  height: 40rpx;
  border-left: 1px solid #eeeeee;

  /deep/ span {
    margin-left: 20rpx;
  }
}

.box6 {
  justify-content: space-between;
  position: fixed;
  // #ifdef APP-PLUS || MP-WEIXIN
  bottom: 0;
  // #endif
  // #ifdef H5
  bottom: var(--window-bottom);
  // #endif
  left: 0;
  border-top: 1px solid #ededed;
  display: flex;
  height: 100rpx;
  overflow: hidden;
  align-items: center;
  width: 100%;
  background: rgba(255, 255, 255, 1);
  color: #333;
  z-index: 99;
  > .navL {
    padding: 0 32rpx;
    display: flex;
    align-items: center;
  }
}

.sp-type {
  color: $u-light-color;
  padding: 10rpx 0;
  font-size: 24rpx;
  overflow: hidden;

  text-overflow: ellipsis;

  white-space: nowrap;
}


.sp-number {
  font-weight: bold;

  display: flex;
  justify-content: space-between;
  > .sp-price {
    /deep/ span:nth-of-type(1) {
      font-size: 38rpx;
    }
    /deep/ span:nth-of-type(2) {
      font-size: 24rpx;
    }
  }
}
.priceDetail-flowPrice {
  font-weight: bold;
  padding-left: 20rpx;
  > span:nth-of-type(1) {
    font-size: 38rpx;
  }
}

.prices {
  display: flex;
  flex-direction: column;

  > .discountPrice {
    align-items: center;
    display: flex;
    font-size: 24rpx;
    color: rgb(201, 199, 199);
  }
}
.discount-details {
  margin-left: 10px;
  color: #666;
  padding: 4rpx 10rpx;
  border-radius: 100px;
  background: rgba(201, 199, 199, 0.3);
}
.discount-item {
  display: flex;
  margin: 40rpx 0;
  justify-content: space-between;
  > span:nth-of-type(1) {
    color: #666;
  }
  > span:nth-of-type(2) {
    color: #333;
    font-weight: bold;
  }
}
.discount-title {
  font-size: 36rpx;
  margin-top: 20rpx;
  text-align: center;
}
.discount-way {
  width: 94%;
  margin: 0 3%;
}
.discount-list {
  width: 100%;
}
.promotions-list {
  margin-left: 20rpx;
  > .promotions-item-seckill {
    background: rgba($color: $main-color, $alpha: 0.1);
    font-size: 24rpx;
    color: $main-color;
    display: inline;
    padding: 0rpx 10rpx;
    border-radius: 100px;
  }
}
.store-info {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
  
  .store-name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
  }
}
.cart-list {
  padding: 0  32rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 200rpx 0;
  flex-direction: column;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  margin-top: 20rpx;
}

/* 加载动画 */
.loading-container::before {
  content: '';
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff5000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
