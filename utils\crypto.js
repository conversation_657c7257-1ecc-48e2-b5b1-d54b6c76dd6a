// utils/crypto.js
import CryptoJS from 'crypto-js'

const key = CryptoJS.enc.Utf8.parse('h5qQptopY62s36CdUY2bHg==') // 16位密钥
const iv = CryptoJS.enc.Utf8.parse('sA4Vnemvc91X5tQw') // 16位初始向量

export default {
  // AES加密
  encrypt(data) {
    if (typeof data === 'object') {
      data = JSON.stringify(data)
    }
    const encrypted = CryptoJS.AES.encrypt(
      CryptoJS.enc.Utf8.parse(data),
      key,
      {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    )
    return encrypted.toString()
  },

  // AES解密
  decrypt(encrypted) {
    const decrypted = CryptoJS.AES.decrypt(encrypted, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    return decrypted.toString(CryptoJS.enc.Utf8)
  }
}
