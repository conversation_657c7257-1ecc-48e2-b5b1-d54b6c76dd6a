const name = "臻小选"; //全局商城name
const schemeName = "臻小选"; //唤醒app需要的schemeName
// 开发环境
const devDomain = {
  shareLink: "http://test.mall.zhenxiaoxuan.com",
  scanAuthNavigation: ["http://test.mall.zhenxiaoxuan.com/"],
  imWebSrc: "http://192.168.1.221:8885",
  baseWsUrl: "ws://192.168.1.221:8885/lili/webSocket",
};

// 开发环境
const testDomain = {
  shareLink: "http://test.mall.zhenxiaoxuan.com",
  scanAuthNavigation: ["http://test.mall.zhenxiaoxuan.com/"],
  imWebSrc: "http://192.168.1.221:8885",
  baseWsUrl: "ws://192.168.1.221:8885/lili/webSocket",
};

// const prodDomain = {
//   shareLink: "http://test.mall.zhenxiaoxuan.com",
//   scanAuthNavigation: ["http://test.mall.zhenxiaoxuan.com/"],
//   imWebSrc: "http://192.168.1.221:8885",
//   baseWsUrl: "ws://192.168.1.221:8885/lili/webSocket",
// };

const prodDomain = {
  shareLink: "https://mall.zhenxiaoxuan.com",
  scanAuthNavigation: ["https://mall.zhenxiaoxuan.com/"],
  imWebSrc: "https://im.api.zhenxiaoxuan.com",
  baseWsUrl: "wss://im.api.zhenxiaoxuan.com/lili/webSocket",
};


//默认开发环境
let envDomain = prodDomain;
//如果是开发环境
if (process.env.NODE_ENV == "development") {
  envDomain = devDomain;
} else if(process.env.NODE_ENV == "test") {
  envDomain = testDomain;
}

export default {
  name: name,
  schemeLink: `${schemeName}://`, //唤起app地址
  downloadLink: "https://www.zhenxiaoxuan.com", //下载地址，下载app的地址
  shareLink: envDomain.shareLink, //分享地址，也就是在h5中默认的复制地址
  appid: "wx6f10f29075dc1b0b", //小程序唯一凭证，即 AppID，可在「微信公众平台 - 设置 - 开发设置」页中获得。（需要已经成为开发者，且帐号没有异常状态）
  aMapKey: "1f78544934b66c9fbc0104117f663973", //在高德中申请Web服务key
  scanAuthNavigation: envDomain.scanAuthNavigation, //扫码认证跳转域名配置 会根据此处配置的路由进行跳转
  iosAppId: "id1564638363", //AppStore的应用地址id 具体在分享->拷贝链接中查看
  logo: "/static/logo.png", //logo地址
  customerServiceMobile: "4006163631", //客服电话
  customerServiceEmail: "<EMAIL>", //客服邮箱
  imWebSrc: envDomain.imWebSrc, //IM接口地址
  baseWsUrl: envDomain.baseWsUrl, // IM WS 地址
  enableGetClipboard: false, //是否启用粘贴板获取 scanAuthNavigation 中的链接，如果匹配则会跳转到对应页面
  enableMiniBarStartUpApp: true, //是否在h5中右侧浮空按钮点击启动app
  /**
   * 如需更换主题请修改此处以及uni.scss中的全局颜色
   */
  mainColor: "#ff3c2a", // 主题色
  lightColor: "#ff6b35", // 高亮主题色
  aiderLightColor: "#ff9f28", // 辅助高亮颜色
  defaultUserPhoto: "/static/missing-face.png", // 默认用户头像
  enableFetchMobileLogin: false // 是否启用获取手机号登录 如果微信小程序提示封禁手机号获取权限 可将此选项设置成false作为备用登录方案
};
