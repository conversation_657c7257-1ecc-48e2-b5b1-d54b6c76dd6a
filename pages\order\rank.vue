<template>
  <view>
    <view class="rank-page" v-show="!loading" :style="{ backgroundColor: backgroundColor }">
      <!-- <u-navbar :background="background" back-icon-color="#fff" :border-bottom="false"></u-navbar> -->
      <!-- 顶部推荐栏 -->
      <view
        class="top-banner"
        :style="{
          height: topBannerHeight + 'rpx',
          backgroundImage: backgroundBg ? `url(${backgroundBg})` : 'url()',
          backgroundSize: '100% 100%',
          backgroundRepeat: 'no-repeat',
        }"
      >
        <view
          class="back-btn"
          :style="{ marginTop: safeAreaInsets.top + 'px' }"
        >
          <u-icon
            name="arrow-left"
            :color="cornerColor"
            @click="goBack"
            size="36"
          ></u-icon>
        </view>
      </view>

      <!-- u-tabs 分类Tab栏 -->
      <view
        class="custom-tabs-bg"
        v-if="tabs.length > 0"
        :style="{ backgroundColor: backgroundColor }"
      >
        <u-tabs
          ref="tabs"
          :list="tabs"
          :is-scroll="false"
          :current="current"
          @change="onTabChange"
          :bg-color="backgroundColor"
          active-color="#fff"
          inactive-color="#b8a08a"
        ></u-tabs>
      </view>

      <!-- 排行榜列表（可滚动） -->
      <scroll-view
        class="rank-list-scroll"
        scroll-y="true"
        :style="{
          height: scrollHeight + 'px',
          backgroundColor: backgroundColor,
          marginTop: tabs.length === 0 ? '0' : '0',
        }"
      >
        <view class="rank-list">
          <view
            v-for="item in rankList"
            :key="item.id"
            class="rank-item"
            @tap="geToGoods(item)"
          >
            <image :src="item.img" class="item-img" :lazy-load="true" />
            <view class="item-info">
              <view class="item-title">{{ item.title }}</view>
              <view class="item-tags">
                <view class="new-tag">
                  <image src="/static/img/new-tag.png" mode="scaleToFill" />
                </view>
                <view class="sales-count"
                  >已售{{ item.buyCount || 0  }}件</view
                >
              </view>
              <view class="item-bottom">
                <view
                  class="price-button"
                  :class="getPriceButtonClass(item.price)"
                >
                  <text class="price-text">
                    <text class="price-symbol">¥</text>
                    {{ Number(item.price) }}</text
                  >
                </view>
                <view class="buy-button" @tap.stop="geToGoods(item)">
                  立即购买
                </view>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    <view class="loading" v-show="loading">
      <u-loading mode="flower" size="65"></u-loading>
    </view>
  </view>
</template>

<script>
import { getPageDataList } from "@/api/article";
export default {
  data() {
    return {
      tabs: [],
      current: 0,
      background: {
        backgroundColor: "#2d211b",
      },
      scrollHeight: 0,
      listWay: [],
      backgroundBg: "",
      backgroundColor: "#2d211b",
      topBannerHeight: 362, // 默认高度，单位rpx
      safeAreaInsets: 0,
      cornerColor: "#fff",
      loading: true, // 是否显示骨架屏组件
    };
  },
  onLoad(opacity) {
    // 初始计算scroll-view高度
    this.getPrivacyList(opacity.id);
    const { safeAreaInsets } = uni.getSystemInfoSync();
    this.safeAreaInsets = safeAreaInsets;
  },
  methods: {
    onTabChange(index) {
      this.current = index;
    },
    goBack() {
      uni.navigateBack();
    },
    // 根据价格长度返回对应的背景图类名
    getPriceButtonClass(price) {
      const priceStr = Number(price).toString();
      const length = priceStr.length;

      if (length <= 4) {
        return "price-bg-default";
      } else if (length <= 6) {
        return "price-bg-medium";
      } else {
        return "price-bg-large";
      }
    },
    // 计算scroll-view高度的方法
    calculateScrollHeight() {
      console.log("calculateScrollHeight", this.tabs.length);
      const topHeightPx = uni.upx2px(
        this.topBannerHeight + (this.tabs.length > 0 ? 90 : 0)
      );
      const windowHeight = uni.getSystemInfoSync().windowHeight;
      this.scrollHeight = windowHeight - topHeightPx;
    },
    // 获取图片信息并设置高度
    getImageInfo(imageUrl) {
      return new Promise((resolve, reject) => {
        // 检查图片URL是否有效
        if (!imageUrl || imageUrl.trim() === "") {
          reject(new Error("图片URL为空"));
          return;
        }

        uni.getImageInfo({
          src: imageUrl,
          success: (res) => {
            resolve(res);
          },
          fail: (err) => {
            console.error("失败详情:", {
              errMsg: err.errMsg,
              errCode: err.errCode,
              imageUrl: imageUrl,
            });
            reject(err);
          },
        });
      });
    },
    getImageSizeByElement(imageUrl) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        const img = new Image();
        img.onload = () => {
          resolve({ width: img.width, height: img.height });
        };
        img.onerror = (err) => {
          reject(new Error("图片加载失败"));
        };
        img.src = imageUrl;
        // #endif

        // #ifndef H5
        reject(new Error("非H5环境不支持此方法"));
        // #endif
      });
    },
    async getPrivacyList(val) {
      const res = await getPageDataList(val);
      if (res.statusCode === 200) {
        const result = JSON.parse(res.data.result.pageData);
        console.log(result);
        this.backgroundBg = result.list[0].options.list[0].img;
        this.backgroundColor = result.list[1].options.list[0].titleWay[0].color;
        this.cornerColor =
          result.list[1].options.list[0].titleWay[0].cornerColor;
        console.log(this.cornerColor);

        // 获取图片信息并计算高度
        try {
          let imageInfo;
          try {
            imageInfo = await this.getImageInfo(this.backgroundBg);
          } catch (firstError) {
            imageInfo = await this.getImageSizeByElement(this.backgroundBg);
          }

          const screenWidth = uni.getSystemInfoSync().windowWidth;
          const imageRatio = imageInfo.height / imageInfo.width;
          const bannerHeightPx = screenWidth * imageRatio;

          // 将px转换为rpx：1rpx = 0.5px
          const systemInfo = uni.getSystemInfoSync();
          const pxToRpxRatio = 750 / systemInfo.windowWidth; // 750rpx对应屏幕宽度
          this.topBannerHeight = bannerHeightPx * pxToRpxRatio;

          this.calculateScrollHeight();
        } catch (error) {
          this.topBannerHeight = 362; // 使用默认高度
          this.calculateScrollHeight();
        }

        console.log("this.tabs", result.list[1].options.list[0].titleWay);

        if (result.list[1].options.list[0].titleWay[0].title == "") {
          this.tabs = [];
          this.calculateScrollHeight();
        } else {
          this.tabs = result.list[1].options.list[0].titleWay.map((item) => {
            const { title, ...rest } = item;
            return { name: title, ...rest };
          });
          this.calculateScrollHeight();
        }
        this.listWay = result.list[1].options.list[0].listWay;
        setTimeout(() => {
          this.loading = false; // 数据加载完成，隐藏加载状态
          this.$nextTick(() => {
            // 页面由隐藏(v-show)切换为显示后，重新初始化tabs以正确计算滑块位置
            if (this.$refs.tabs && this.tabs.length > 0) {
              this.$refs.tabs.init();
            }
          });
        }, 300);
       
      }
    },
    geToGoods(item) {
      uni.navigateTo({
        url: `/pages/product/goods?id=${item.id}&goodsId=${item.goodsId}`,
      });
    },
  },
  computed: {
    rankList() {
      return (this.listWay || []).filter(
        (item) => item.___index === this.current
      );
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  overflow: hidden;
}
.rank-page {
  background: #231815;
  min-height: 100vh;
  overflow: hidden;
}
.top-banner {
  width: 100%;
  padding: 40rpx 0 30rpx 0;
  text-align: center;
  color: #fff;
}
.back-btn {
  width: 100rpx;
}
.banner-title {
  font-size: 24rpx;
  margin-bottom: 10rpx;
  opacity: 0.8;
}
.banner-main {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.banner-sub {
  font-size: 20rpx;
  opacity: 0.7;
}
.tab-bar {
  display: flex;
  background: #2d211b;
  border-radius: 20rpx;
  margin: 20rpx 20rpx 0 20rpx;
  overflow: hidden;
}
.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  color: #b8a08a;
  font-size: 28rpx;
  background: transparent;
}
.tab-item.active {
  color: #fff;
  font-weight: bold;
  border-bottom: 4rpx solid #e6c28b;
  background: #2d211b;
}
.rank-list {
  margin: 0 32rpx;
  position: relative;
  padding-bottom: 10rpx;
}
.rank-item {
  width: 686rpx;
  height: 232rpx;
  display: flex;
  align-items: center;
  background: #fff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  padding: 22rpx 34rpx 32rpx 20rpx;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.rank-badge {
  /* 已移除的排行榜徽章样式 */
  display: none;
}
/* .rank-badge-1 {
  background: #e6c28b;
  color: #fff;
}
.rank-badge-2 {
  background: #e0e0e0;
  color: #b8a08a;
}
.rank-badge-3 {
  background: #e6b1a1;
  color: #fff;
} */
.item-img {
  width: 190rpx;
  height: 182rpx;
  border-radius: 10rpx;
  margin-right: 50rpx;
  background: #f5f5f5;
  /* margin-left: 80rpx; */
}
.item-info {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.item-title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #131316;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  margin-bottom: 10rpx;
}

.item-tags {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.new-tag {
  position: relative;
  image {
    width: 56rpx;
    height: 30rpx;
    margin-right: 4rpx;
  }
}

.sales-count {
  font-family: Alibaba PuHuiTi, Alibaba PuHuiTi;
  font-weight: 400;
  font-size: 20rpx;
  color: #ff5235;
}

.item-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-button {
  width: 192rpx;
  height: 48rpx;
  background: url("/static/img/price_bg.png") no-repeat;
  background-size: 100% 100%;
  border-radius: 20rpx;
  position: relative;
  overflow: hidden;
}

.price-bg-default {
  background: url("/static/img/price_bg.png") no-repeat;
  background-size: 100% 100%;
}

.price-bg-medium {
  width: 234rpx;
  background: url("/static/img/price_bg1.png") no-repeat;
  background-size: 100% 100%;
}

.price-bg-large {
  width: 266rpx;
  background: url("/static/img/price_bg2.png") no-repeat;
  background-size: 100% 100%;
}

.price-text {
  width: 100%;
  display: inline-block;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  padding-right: 40rpx;
  line-height: 48rpx;
  .price-symbol {
    font-size: 24rpx;
    margin-right: 6rpx;
  }
}

.buy-button {
  width: 263rpx;
  height: 44rpx;
  background: linear-gradient(90deg, rgba(255, 114, 35, 0) 0%, #ff681f 100%);
  border-radius: 10rpx 10rpx 10rpx 10rpx;
  font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  text-shadow: 0px 4px 12px rgba(150, 6, 6, 0.18);
  position: absolute;
  right: 34rpx;
  text-align: right;
  padding: 0 20rpx;
  line-height: 44rpx;
}
.custom-tabs-bg {
  width: 100%;
  padding: 0 20rpx;
  height: 90rpx;
}
.rank-list-scroll {
  /* 高度由js动态设置 */
  box-sizing: border-box;
}
.badge-img {
  /* 已移除的徽章图片样式 */
  display: none;
}
.loading {
  text-align: center;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
