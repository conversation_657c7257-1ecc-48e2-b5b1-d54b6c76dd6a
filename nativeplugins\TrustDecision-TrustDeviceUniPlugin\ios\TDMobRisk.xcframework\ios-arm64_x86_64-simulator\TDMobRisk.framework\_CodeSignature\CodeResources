<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/TDMobRisk.h</key>
		<data>
		sSRrCMY5/NMMdvlTKYIsAMztlBI=
		</data>
		<key>Headers/TDMobRiskManager.h</key>
		<data>
		4uLJKXY9q+YsE2zmQHy10ja1sqw=
		</data>
		<key>Info.plist</key>
		<data>
		3mwqibX7Fcn691ZiQFDiCeWV9Eg=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		2pOK0yhRQfLkUGWUpdmfO74W/Mg=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/TDMobRisk.h</key>
		<dict>
			<key>hash</key>
			<data>
			sSRrCMY5/NMMdvlTKYIsAMztlBI=
			</data>
			<key>hash2</key>
			<data>
			Rc9QPI64hvt8G4XXjgmBkAk75omUsnJrPBU3rH99ZZ0=
			</data>
		</dict>
		<key>Headers/TDMobRiskManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			4uLJKXY9q+YsE2zmQHy10ja1sqw=
			</data>
			<key>hash2</key>
			<data>
			zA12KBbNJ4iTJiRo35JXYGqyQOuENWzf3Agym68/GeA=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			2pOK0yhRQfLkUGWUpdmfO74W/Mg=
			</data>
			<key>hash2</key>
			<data>
			k/1YNqeHdxf7mUj/2XA+HzTxymMVND//YA1OmiAzjeM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
