<template>
  <div class="wrapper">
    <u-tabs
      :list="list"
      :is-scroll="false"
      :active-color="lightColor"
      :current="current"
      @change="
        (i) => {
          current = i;
        }
      "
    >
    </u-tabs>

    <div class="empty" v-if="couponsList.length <= 0">
      <!-- <u-empty text="暂无优惠券" mode="coupon"></u-empty> -->
      <image
        src="/static/img/coupon_empty.png"
        mode="scaleToFill"
        style="width: 322rpx; height: 322rpx;"
      />
      <view class="empty-text" style="font-size: 28rpx; color: #666;">暂无优惠券</view>
    </div>
    <view v-for="(item, index) in couponsList" :key="index">
      <view class="coupon-item">
        <view class="left">
          <!-- <view class="wave-line">
            <view class="wave" v-for="(item, index) in 12" :key="index"></view>
          </view> -->
          <view class="message">
            <view>减免卷</view>
            <view>
              <text v-if="item.couponType == 'DISCOUNT'">{{ item.discount }}折</text>
              <text v-else>{{ item.price }} <text class="unit-price">元</text> </text>
            </view>
            <view>满{{ item.consumeThreshold | unitPrice }}元可用</view>
          </view>
          <!-- <view class="circle circle-top"></view>
          <view class="circle circle-bottom"></view> -->
        </view>
        <view class="right">
          <view class="desc">
            <view class="scope_name">
              {{ item.price }}元立减劵-{{ getRemainDays(item.endTime) }}日
            </view>
            <!-- <view v-if="item.scopeType">
              <span v-if="item.scopeType == 'ALL' && item.storeId == '0'">全平台</span>
              <span v-if="item.scopeType == 'PORTION_GOODS_CATEGORY'">仅限品类</span>
              <view v-else
                >{{
                  item.storeName == "platform" ? "全平台" : item.storeName + "店铺"
                }}使用</view
              >
            </view> -->
            <view class="reason" v-if="item.reason">{{ item.reason }}</view>
            <view class="end-time">有效期至:{{ item.endTime }}</view>
            <!-- 使用详情 -->
            <view>
              <view class="usage_det_btn" @click="toggleDetail(index)">
                使用详情
                <u-icon
                  :name="showDetailIndex === index ? 'arrow-up' : 'arrow-down'"
                  color="#999999"
                  size="28"
                  style="margin-left: 20rpx;"
                ></u-icon>
              </view>
            </view>
          </view>
          <view
            class="receive"
            v-if="current == 0 && !routerVal.selectedCoupon.includes(item.id)"
            @click="clickWay(item)"
          >
            <text>去使用</text>
          </view>
          <view class="used" v-if="current == 0 && routerVal.selectedCoupon.includes(item.id)" @click="clickWay(item)">
            <text>取消使用</text>
          </view>
          <!-- <view class="bg-quan">券</view> -->
        </view>
      </view>
      <view v-if="showDetailIndex === index" class="usage-detail-card" :key="'detail-' + index">
        <view class="usage-row">· 使用范围：{{ item.scopeTypeText || '指定商品' }}</view>
        <view class="usage-row">· 范围描述：{{ item.scopeDesc || '【定向商品专用】' }}{{ item.price }}元大额立减券({{ getRemainDays(item.endTime) }}日有效, APP专用)</view>
        <view class="usage-row">· 使用渠道：{{ item.channel || 'APP' }}</view>
      </view>
    </view>
  </div>
</template>
<script>
import { useCoupon } from "@/api/trade.js";

export default {
  data() {
    return {
      lightColor: this.$lightColor,
      current: 0,
      list: [
        {
          name: "可用优惠券",
        },
        {
          name: "不可用优惠券",
        },
      ],
      couponsList: [], //优惠券集合
      params: {
        //传参
        memberCouponStatus: "NEW", //优惠券状态
        pageNumber: 1,
        pageSize: 10,
        scopeId: "", //商品skuid
        storeId: "", //店铺id
        totalPrice: "", //价格
      },
      routerVal: "", //上级传参
      showDetailIndex: null, // 当前展开详情的索引
    };
  },
  onLoad(options) {
    this.routerVal = options;
  },
  watch: {
    current(val) {
      console.log(this.$store.state.cantUseCoupons);
      val == 0
        ? (this.couponsList = this.$store.state.canUseCoupons)
        : (this.couponsList = this.$store.state.cantUseCoupons);
    },
  },

  mounted() {
    this.init();
    console.log(this.routerVal);
  },

  methods: {
    /**
     * 从vuex中拿取优惠券信息
     */
    init() {
      this.couponsList = this.$store.state.canUseCoupons;
      console.log(this.couponsList);
    },
    /**
     * 领取优惠券
     */
    clickWay(coupon) {
      useCoupon({
        memberCouponId: coupon.id,
        used: !this.routerVal.selectedCoupon.includes(coupon.id),
        way: this.routerVal.way,
      }).then((res) => {
        if (res.data.success) {
          uni.navigateBack();
        } else {
          uni.showToast({
            title: res.data.message,
            duration: 2000,
            icon: "none",
          });
        }
      });
    },
    getRemainDays(endTime) {
      // 如果是字符串，先转成时间戳
      if (typeof endTime === 'string') {
        // 兼容 "2025-06-30 00:00:00" 这种格式
        endTime = endTime.replace(/-/g, '/'); // 兼容iOS
        endTime = new Date(endTime).getTime();
      }
      // 如果endTime是秒，转成毫秒
      if (endTime < 1e12) endTime = endTime * 1000;
      const now = Date.now();
      const diff = endTime - now;
      if (diff <= 0) return 0;
      return Math.ceil(diff / (1000 * 60 * 60 * 24));
    },
    toggleDetail(idx) {
      this.showDetailIndex = this.showDetailIndex === idx ? null : idx;
    },
  },
};
</script>
<style scoped lang="scss">
.desc {
  height: 220rpx;
  flex: 2;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 40rpx 0 40rpx 20rpx;
  box-sizing: border-box;
  .scope_name {
    font-weight: 500;
    font-size: 32rpx;
    color: #000000 !important;
  }
}
.end-time,
.reason {
  color: #999;
  line-height: 1.5;
  font-size: 20rpx;
}

.empty {
  margin-top: 20px;
  text-align: center;
}
.wrapper {
  background: #f9f9f9;
  overflow: hidden;
}
.coupon-item {
  display: flex;
  align-items: center;
  width: 686rpx;
  height: 220rpx;
  border-radius: 20rpx;
  margin: 20rpx auto 0;
  position: relative;
  background: url('/static/img/Coupon_bg.png') ;
  background-size: 100% 100%;
  .left {
    width: 210rpx;
    height: 220rpx;
    // background: #FCEFEF;
    position: relative;
    border-radius: 20rpx;
    .message {
      color: #FF5134;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      margin-top: 30rpx;
      view:nth-child(1) {
        font-weight: 400;
        font-size: 24rpx;
      }
      view:nth-child(2) {
        font-weight: bold;
        font-size: 60rpx;
        .unit-price {
          font-size: 24rpx;
        }
      }

      view:nth-child(3) {
        font-size: $font-sm;
      }
    }

    .wave-line {
      height: 220rpx;
      width: 8rpx;
      position: absolute;
      top: 0;
      left: 0;
      // background-color: $light-color;
      overflow: hidden;

      .wave {
        width: 8rpx;
        height: 16rpx;
        background-color: #ffffff;
        border-radius: 0 16rpx 16rpx 0;
        margin-top: 4rpx;
      }
    }
    .circle {
      width: 40rpx;
      height: 40rpx;
      background-color: $bg-color;
      position: absolute;
      border-radius: 50%;
      z-index: 111;
    }
    .circle-top {
      top: -20rpx;
      right: -20rpx;
    }
    .circle-bottom {
      bottom: -20rpx;
      right: -20rpx;
    }
  }

  .right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 450rpx;
    font-size: $font-sm;
    height: 220rpx;
    // background-color: #ffffff;
    overflow: hidden;
    position: relative;
    > view:nth-child(1) {
      color: #666666;
      margin-left: 20rpx;

      > view:nth-child(1) {
        color: #ff6262;
        font-size: 30rpx;
      }
    }

    .receive {
      color: #ffffff;
      // background-color: $main-color;
      border-radius: 50%;
      width: 86rpx;
      height: 86rpx;
      text-align: center;
      // margin-right: 30rpx;
      vertical-align: middle;
      position: relative;
      z-index: 2;
      width: 134rpx;
      height: 52rpx;
      line-height: 52rpx;
      background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
      border-radius: 190rpx 190rpx 190rpx 190rpx;
    }

    .used {
      color: #ffffff;
      background-color: black;
      border-radius: 50%;
      width: 86rpx;
      height: 86rpx;
      text-align: center;
      margin-left: 30rpx;
      vertical-align: middle;
      padding-top: 8rpx;
      position: relative;
      z-index: 2;
      width: 134rpx;
      height: 52rpx;
      background: linear-gradient( 90deg, #FF5235 0%, #FF9500 100%);
      border-radius: 190rpx 190rpx 190rpx 190rpx;
    }

    .bg-quan {
      width: 244rpx;
      height: 244rpx;
      border: 6rpx solid $main-color;
      border-radius: 50%;
      opacity: 0.1;
      color: $main-color;
      text-align: center;
      padding-top: 30rpx;
      font-size: 130rpx;
      position: absolute;
      right: -54rpx;
      bottom: -60rpx;
    }
  }
}

.usage-detail-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx 0 rgba(0,0,0,0.04);
  padding: 24rpx 20rpx;
  margin: 0 auto 20rpx auto;
  color: #888;
  font-size: 24rpx;
  line-height: 1.8;
  width: 686rpx;
}
.usage-row {
  margin-bottom: 6rpx;
  &:last-child {
    margin-bottom: 0;
  }
}
.usage_det_btn {
  color: #888;
  font-size: 24rpx;
  margin-top: 10rpx;
  cursor: pointer;
  display: flex;
  align-items: center;
  user-select: none;
}
.arrow-down::after {
  content: '▼';
  font-size: 20rpx;
  margin-left: 8rpx;
}
.arrow-right::after {
  content: '▶';
  font-size: 20rpx;
  margin-left: 8rpx;
}
</style>
