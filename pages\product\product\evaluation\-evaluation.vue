<template>
  <view class="evaluate-box">
    <view class="e-header">
      <view class="header-left">
        <view class="evaluate-title">商品评价</view>
        <text class="evaluate-num">{{ commDetail.total || '0' }}</text>
      </view>
      <view class="header-right" @click="toComment(goodsDetail.goodsId, goodsDetail.grade)">
        <text>全部评价</text>
        <u-icon name="arrow-right" color="#999999" size="26"></u-icon>
        <!-- <u-icon name="arrow-right" color="#999999" size="32"></u-icon> -->
      </view>
    </view>
    <div v-if="commDetail && commDetail.records && commDetail.records.length > 0" class="comment-list-wrapper">
      <view class="eva-box" v-for="(commItem,commIndex) in commDetail.records.slice(0,1)" :key="commIndex">
        <view class="section-info">
          <view class="user-info">
            <!-- <u-avatar mode="circle" size="60" class="portrait" :src="commItem.memberProfile"></u-avatar> -->
            <u-image shape="circle" :lazy-load="true" width="80" height="80"
            :src="commItem.memberProfile || userImage "></u-image>
            <view class="name-star" style="margin-left: 10rpx;">
             
              <text class="name">{{ commItem.memberName | noPassByName }}</text>
              <u-rate :count="5" :current="commItem.descriptionScore || 5" active-color="#FFC400" inactive-color="#ccc" :disabled="true" size="28"></u-rate>
            </view>
          </view>
          <text class="comment-date">{{ commItem.createTime | dateFormat('yyyy-MM-dd') }}</text>
        </view>
        <view class="section-contant">
          <u-read-more ref="uReadMore" :color="lightColor">
            <rich-text @load="parseLoaded" :nodes="commItem.content " class="con"></rich-text>
          </u-read-more>
          <scroll-view scroll-x class="scroll-x" v-if="commItem.image">
            <view class="img">
              <u-image border-radius="12" class="commImg" width="160rpx" height="160rpx" v-for="(item, index) in commItem.image.split(',')" :src="item" :key="index"
                @click.stop="previewImg(commItem.image, index)"></u-image>
            </view>
          </scroll-view>
        </view>
      </view>
    </div>

    <div v-else class="goodsNoMore">
      <u-empty text="该商品暂无评论" mode="message"></u-empty>
    </div>
  </view>
</template>

<script>
import * as API_Members from "@/api/members.js";
import configs from '@/config/config'
export default {
  data() {
    return {
      lightColor: this.$lightColor,
      userImage:configs.defaultUserPhoto,
      // 评论集合
      commDetail: [],
      grade: "",
      // 评论分页提交数据
      params: {
        pageNumber: 1,
        pageSize: 10,
        grade: "",
      },
    };
  },
  props: {
    goodsDetail: {
      default: {},
      type: Object,
    },
  },

  watch: {
    goodsDetail: {
      handler(val) {
        if (val && val.goodsId) {
          this.grade = val.grade;
          this.getGoodsCommentsMethods();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
  
  },
  methods: {
    parseLoaded() {
      this.$refs.uReadMore.init();
    },

    // 获取商品评论
    getGoodsCommentsMethods() {
      API_Members.getGoodsComments(this.goodsDetail.goodsId, this.params).then(
        (res) => {
          this.commDetail = res.data.result;
        }
      );
    },
    toComment(id, grade) {
      uni.navigateTo({
        url: `/pages/product/comment?id=${id}&grade=${grade}`,
      });
    },
    /**
     * 点击图片放大或保存
     */
    previewImg(url, index) {
      uni.previewImage({
        urls: url,
        indicator: "number",
        current: index,
      });
    },
  },
  filters: {
    dateFormat(value, format) {
      const date = new Date(value);
      const o = {
        'M+': date.getMonth() + 1, // 月份
        'd+': date.getDate(), // 日
        'h+': date.getHours(), // 小时
        'm+': date.getMinutes(), // 分
        's+': date.getSeconds(), // 秒
        'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
        'S': date.getMilliseconds() // 毫秒
      };
      if (/(y+)/.test(format)) {
        format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
      }
      for (let k in o) {
        if (new RegExp('(' + k + ')').test(format)) {
          format = format.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)));
        }
      }
      return format;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "../product.scss";
.evaluate-box {
  background: #fff;
  border-radius: 16rpx;
  margin: 20rpx 32rpx !important;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
.e-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // padding-bottom: 20rpx;
  // border-bottom: 1rpx solid #eee;
  // margin-bottom: 20rpx;

  .header-left {
    display: flex;
    align-items: baseline;
    .evaluate-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-right: 10rpx;
      &::before { 
        display: none;
      }
    }
    .evaluate-num {
      font-size: 28rpx;
      color: #666;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #999;
    text {
      margin-right: 5rpx;
    }
  }
}

.comment-list-wrapper {
  padding: 0;
}

.commImg {
  margin-right: 12rpx;
  margin-bottom: 10rpx;
  display: inline-block;
}
.name {
  color: #262626;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.eva-section-btn {
  display: none;
}

.goodsNoMore {
  padding: 20rpx 0;
  text-align: center;
  color: $u-tips-color;
}

/* 评价 */
.eva-section {
  display: flex;
  flex-direction: column;
  background: #fff;
  margin-bottom: 0;
  padding: 0;
}

.eva-box {
  padding: 0;
  border-bottom: none;

  .section-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .user-info {
      display: flex;
      align-items: center;
    }

    .portrait {
      flex-shrink: 0;
      width: 80rpx;
      height: 80rpx;
      border-radius: 100px;
      margin-right: 20rpx;
    }

    .name-star {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .comment-date {
      font-size: 24rpx;
      color: #999;
    }
  }

  .section-contant {
    display: flex;
    flex-direction: column;
    margin-bottom: 20rpx;

    .con {
      font-size: 26rpx;
      line-height: 46rpx;
      color: #333;
      padding: 0;
    }
  }
}
</style>
