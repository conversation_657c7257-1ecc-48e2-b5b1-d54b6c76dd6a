<template>
  <view class="container">
    <view class="cell-group">
      <u-cell-item
       v-for="(item, index) in privacyList" :key="index"
        :title="item.title"
        @click="openDetail(item.id)"
        :border-bottom="index !== privacyList.length - 1"
      ></u-cell-item>
      
    </view>
    <view class="safe-logout-btn" @click="safeLogout">安全退出</view>
  </view>
</template>

<script>
import { getPrivacyList2 } from "@/api/article";
import { quiteLoginOut } from "@/utils/filters.js";

export default {
  data() {
    return {
      privacyList: []
    };
  },
  onLoad(options) {
    this.getPrivacyList('AUTH_CATE_V2')
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },
    openDetail(type) {
      uni.navigateTo({ url: `/pages/mine/set/privacyDetail?type=${type}` });
    },
    safeLogout() {
      // 退出登录逻辑
      quiteLoginOut(this);
    },
    async getPrivacyList(val){
      const res = await getPrivacyList2(val);
      if (res.statusCode===200) {
        this.privacyList = res.data.result;
      }
      
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: #fafafa;
  height: 100%;
  overflow: hidden;
}
.navbar {
  display: flex;
  align-items: center;
  height: 100rpx;
  background: #fff;
  padding: 0 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.navbar-title {
  flex: 1;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
}
.cell-group {
  margin: 30rpx 20rpx 0 20rpx;
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}
.safe-logout-btn {
  width: 670rpx;
  height: 100rpx;
  border-radius: 78rpx 78rpx 78rpx 78rpx;
  line-height: 100rpx;
  text-align: center;
  font-weight: 400;
  font-size: 32rpx;
  color: #FFFFFF;
  border-radius: 45rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  box-shadow: 0 4rpx 16rpx rgba(255, 124, 52, 0.15);
  position: fixed;
  bottom: 50rpx;
  left: 50%;
  transform: translateX(-50%);
}
</style>
