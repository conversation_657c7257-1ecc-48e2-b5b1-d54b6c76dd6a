
<template>
  <div class="layout">
    <div class="left_img" @click="modelNavigateTo(res.list[0])">

      <u-image width="336rpx" height="220rpx" class="image-mode" :src="res.list[0].img">
        <u-loading slot="loading"></u-loading>
      </u-image>
      <image
        src="/static/index/left_bg.png"
        mode="scaleToFill"
        class="left_bg"
      />
    </div>
    <div class=" right">
      <div class="bg view-height-75" @click="modelNavigateTo(res.list[1])">
        <u-image width="334rpx" height="102rpx" class="image-mode" :src="res.list[1].img" alt>
          <u-loading slot="loading"></u-loading>
        </u-image>
        <image
          src="/static/index/right_bg1.png"
          mode="scaleToFill"
          class="right_bg1"
        />
      </div>
      <div class="bg view-height-75" @click="modelNavigateTo(res.list[2])">
        <u-image width="334rpx" height="102rpx" class="image-mode" :src="res.list[2].img" alt>
          <u-loading slot="loading"></u-loading>
        </u-image>
        <image
          src="/static/index/right_bg2.png"
          mode="scaleToFill"
          class="right_bg2"
        />
      </div>
    </div>
  </div>
</template>
<script>

import { modelNavigateTo } from "./tpl";
export default {
  title: "左一右二",
  props: ["res"],
  data() {
    return {
      modelNavigateTo,
    };
  },
  mounted() {},
};
</script>
<style lang="scss" scoped>
@import "./tpl.scss";
.layout {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-size: cover;
  margin: 0 !important;
  padding: 0 30rpx;
}
.left_img {
  width: 336rpx;
  height: 240rpx;
  position: relative;
}
.left_bg {
  position: absolute;
  bottom: -4rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 319rpx;
  height: 53rpx;
}
.bg {
  position: relative;
  height: 120rpx;
}
.right_bg1 {
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 311rpx;
  height: 47rpx;

}
.right_bg2 {
  position: absolute;
  bottom: -6rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 311rpx;
  height: 47rpx;
}
.right {
  width: 334rpx;
  height: 240rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20rpx;
}

</style>