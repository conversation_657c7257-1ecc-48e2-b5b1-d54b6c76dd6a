/**
 * 公共API
 */
import { http, Method } from "@/utils/request.js";
import api from "@/config/api.js";
import CryptoJS from '@/utils/crypto.js';

/**
 * 获取图片验证码
 */
export function getCaptcha() {
  return http.request({
    url: `${api.common}/common/captcha/getCaptcha`,
    method: Method.GET,
    responseType: 'arraybuffer'
  });
}


/**
 * 获取图片验证码校验
 */
export function checkGraphicCaptcha(type, pictureCode) {
  return http.request({
    url: `${api.common}/common/captcha/check/${type}`,
    method: Method.GET,
    data: { pictureCode },
    message: false,
  });
}

/**
 * 获取地区数据
 * @param id
 */
export function getRegionsById(id = 0) {
  return http.request({
    url: `${api.common}/common/region/item/${id}`,
    method: Method.GET,
    message: false,
  });
}

// 获取IM接口前缀
export function getIMDetail() {
  return http.request({
    url: `${api.common}/IM`,
    method: Method.GET,
    message: false,
  });
}

/**
 * 文件上传地址
 * @type {string}
 */
export const upload = api.common + "/common/upload/file";

/**
 * 获取多个字典项
 * @param {Array<string>} types - 字典类型数组，如 ["education","marriage_status"]
 */
export function getDictManyList(types) {
  return http.request({
    url: `/inst/api/dict/many/list`,
    method: Method.POST,
    data: types,
    message: false,
  });
}

/**
 * 文件上传地址
 * @type {string}
 */
export const upload_financial = api.common + "/common/upload/privacyfile";



/**
 * 获取银行卡验证码
 * @param {Object} params { bankCode, mobile, cardNumber, clientType }
 */
export function getBankCardAuthCode(params) {
  return http.request({
    url: '/inst/api/bankUser/authSign',
    method: Method.POST,
    data: params,
    message: false,
  });
}



/**
 * 新增绑卡用户
 * @param {Object} params { cardNumber, verifyCode, clientType }
 */
export function addBankUser(params) {
  return http.request({
    url: '/inst/api/bankUser/add',
    method: Method.POST,
    data: params,
    message: false,
  });
}

/**
 * 查询银行卡列表
 * @returns {Promise} 返回银行卡列表数据
 */
export function getBankUserList() {
  return http.request({
    url: '/inst/api/bankUser/list',
    method: Method.POST,
    needToken: true,
    message: false,
  });
}

/**
 * 银行卡还款确认
 * @param {Object} params 还款参数
 * @param {string} params.bankcardNo 银行卡号
 * @param {string} params.isAllRepay 是否全额还款 Y/N
 * @param {number} params.loanId 借款ID
 * @param {number} params.nper 期数
 * @param {string} params.payType 支付类型
 * @param {number} params.repayAmount 还款金额
 * @param {string} params.smsCode 短信验证码
 * @returns {Promise} 返回还款结果
 */
export function confirmRepay(params) {
  return http.request({
    url: '/inst/api/repay/confirm',
    method: Method.POST,
    needToken: true,
    data: params,
    message: false,
  });
}

/**
 * 获取预还款信息
 * @param {string|number} loanId - 借款ID
 * @returns {Promise} 返回预还款详细信息
 */
export function getPreRepayInfo(loanId) {
  return http.request({
    url: `/inst/api/repay/preRepay/${loanId}`,
    method: Method.GET,
    needToken: true,
    message: false,
  });
}

// 检查臻享卡显示状态
export function checkEnjoyCardStatus() {
  return http.request({
    url: '/inst/api/getHomeCardInfo',
    method: Method.POST,
    message: false,
  });
}

// 获取臻享卡分期预信息
export function getLoanStagingPreInfo(params) {
  return http.request({
    url: '/inst/api/loan/staging/preInfo',
    method: Method.GET,
    data: params,
    message: false
  });
}

/**
 * 获取借款记录
 * @param {Object} params - 查询参数
 */
export function getLoanListInfo(params) {
  return http.request({
    url: '/inst/api/loan/loanListInfo',
    method: Method.POST,
    needToken: true,
    data: params,
    message: false,
  });
}

/**
 * 获取借款记录-分期还款明细
 * @param {Object} params - 查询参数 { loanId: number }
 */
export function getPeriodsDetailInfo(params) {
  return http.request({
    url: '/inst/api/loan/periodsDetailInfo',
    method: Method.POST,
    needToken: true,
    data: params,
    message: false,
  });
}

/**
 * 获取用户可分期期数
 * @returns {Promise} 返回可分期期数数据
 */
export function getCanPeriods() {
  return http.request({
    url: '/inst/api/user/auth/canPeriods',
    method: Method.GET,
    message: false,
  });
}

/**
 * 极光推送注册ID上报
 * @param {string} regId 极光推送注册ID
 * @param {string} clientType 客户端类型
 * @returns {Promise} 返回上报结果
 */
export function reportJPushRegId(regId,clientType) {
  // 对regId进行MD5加密
  const encryptedRegId = CryptoJS.encrypt(regId).toString();
  const params = { regId: encryptedRegId };
  return http.request({
    url: `/ops/third/jpush/save`,
    method: Method.POST,
    message: false,
    data: params,
    header: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'clientType': clientType
    }
  });
}
