<template>
  <view class="face-auth-page">
    <view class="auth-section">
      <view class="auth-avatar-box">
        <image
          src="/static/img/avatar-auth.png"
          mode="aspectFit"
          class="auth-avatar"
        />
      </view>
      <view class="auth-desc-box">
        <view class="auth-desc-title">您知悉并同意应用提供者：</view>
        <view class="auth-desc-list">
          <view>• 收集、使用您本人的身份信息和人脸图像</view>
          <view>• 向合法数据持有者核验您的身份信息</view>
          <view>• 本操作数据仅用于身份核实，安全可靠</view>
        </view>
      </view>
      <view class="auth-line-box">
        <view class="auth-checkbox-box">
          <u-checkbox
            v-model="checked"
            shape="circle"
            active-color="#ff6b35"
            size="26"
          ></u-checkbox>
          <text class="auth-link"
            >上述为个人敏感信息，您知悉并同意<text
              class="auth-protocol"
              @click="goToProtocol"
              >《个人信息授权协议》</text
            >，如拒绝，将无法使用本功能。</text
          >
        </view>
        <view class="auth-btn-box">
          <u-button
            type="primary"
            class="auth-btn"
            @click="continueAuth"
            :disabled="isAuthSuccess"
            :loading="isProcessing"
            >{{ getButtonText() }}</u-button
          >
        </view>
      </view>
    </view>
    <u-popup
      v-model="showCreditModal"
      mode="bottom"
      border-radius="16"
      :closeable="true"
    >
      <view style="padding: 60rpx 40rpx 0">
        <view class="credit-modal-header">
          <text class="credit-modal-title">{{ title }}</text>
          <!-- <u-icon name="close" size="36" color="#bbb" class="credit-modal-close" @click="closeCreditModal" /> -->
        </view>
        <!-- <u-tabs :list="tbaList"  :current="currentTitle" @change="tabChange"
             active-color="#333" inactive-color="#666" :bar-style="barStyle"
            ></u-tabs> -->
        <view class="credit-modal">
          <scroll-view scroll-y class="credit-modal-content">
            <view class="credit-modal-text">
              <u-parse :html="content" :tag-style="style"></u-parse>
            </view>
          </scroll-view>
          <button
            class="credit-modal-btn"
            @click="closeCreditModal"
            :disabled="isProcessing"
          >
            {{ isProcessing ? "处理中..." : "同意协议并申请" }}
          </button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { getFaceParam } from "@/api/safe.js";
// #ifdef APP-PLUS
const ocr = uni.requireNativePlugin("DC-WBOCRService");
//如果只使用人脸插件，请注释上面的OCR插件，同时依赖下面的Normal与Face插件
// const normal = uni.requireNativePlugin('DC-WBNormal');
const face = uni.requireNativePlugin("DC-WBFaceService");
const h5FaceVerifyPlugin = uni.requireNativePlugin("DC-WBH5FaceVerifyService");
// #endif

import { getFaceResult, getAuthAssessmentAmount } from "@/api/safe.js";
import { getArticleDetailByType } from "@/api/article.js";
import { requestAndroidPermission } from "@/uni_modules/x-perm-apply-instr/js_sdk/index.js";
export default {
  data() {
    return {
      showAuth: true,
      checked: true,
      faceResult: "",
      faceImg: "",
      userInfo: {},
      showCreditModal: false,
      title: "",
      content: "",
      creditModalContent: `
          1. 获取您的人脸信息，用于身份核验。
          2. 获取您的手机号码，用于登录和授权。
      `,
      style: {
        img: "width: 710rpx;height:502rpx",
      },
      // 添加状态管理
      isProcessing: false, // 是否正在处理中
      isAuthSuccess: false, // 是否认证成功
      useH5Face: false, // 是否使用H5刷脸方案（APP内H5或H5端）
    };
  },
  // 添加页面生命周期
  onShow() {
    // 页面显示时不重置认证成功状态，避免认证成功后返回时状态被重置
    // this.resetPageState();
    
    // #ifdef H5
    // H5端页面显示时检查URL参数，处理人脸识别返回
    // this.handleH5FaceAuthCallback();
    // #endif
  },
  onLoad(options) {
    // 页面加载时重置状态
    this.resetPageState();
    this.getLocation();

    // #ifdef H5
    // 在 Web 端初始化同盾科技 SDK
    this.initTrustDevice();
    
    // 处理H5人脸识别返回时的URL参数
    this.handleH5FaceAuthCallback(options);
    // #endif
  },
  methods: {
    getLocation() {
      console.log("获取位置信息");
      uni.getLocation({
        type: "wgs84",
        success: (res) => {
          console.log("获取位置信息", res);
        },
      });
    },
    // 重置页面状态
    resetPageState() {
      this.showAuth = true;
      this.checked = true;
      this.faceResult = "";
      this.faceImg = "";
      this.userInfo = {};
      this.showCreditModal = false;
      this.title = "";
      this.content = "";
      this.isProcessing = false;
      this.isAuthSuccess = false; // 重置认证成功状态
    },
    continueAuth() {
      console.log(
        "按钮点击，当前状态 - isProcessing:",
        this.isProcessing,
        "isAuthSuccess:",
        this.isAuthSuccess
      );

      if (!this.checked) {
        return uni.showToast({ title: "请勾选同意协议", icon: "none" });
      }

      // 防止重复点击
      if (this.isProcessing || this.isAuthSuccess) {
        console.log(
          "阻止重复点击 - isProcessing:",
          this.isProcessing,
          "isAuthSuccess:",
          this.isAuthSuccess
        );
        return;
      }

      this.isProcessing = true;
      this.showAuth = false;

      // #ifdef APP-PLUS
      console.log("开始人脸识别");

      // 使用 uni.getSystemInfo 动态判断平台
      uni.getSystemInfo({
        success: (res) => {
          console.log("系统信息:", res);

          if (res.platform === "android") {
            // 安卓平台处理
            console.log("当前平台为Android，申请相机权限");
            requestAndroidPermission("android.permission.CAMERA", {
              title: "权限申请说明",
              content: "为了人脸识别验证,我们需要申请您设备的相机权限",
            })
              .then((status) => {
                console.log("Android相机权限状态", status);
                if (status === 1) {
                  if (this.useH5Face) {
                    this.enterH5FaceVerify();
                  } else {
                    this.startWbFaceVerifyService();
                  }
                } else {
                  this.isProcessing = false;
                  uni.showToast({
                    title: "需要相机权限才能进行人脸识别",
                    icon: "none",
                  });
                }
              })
              .catch((err) => {
                console.error("权限申请失败:", err);
                this.isProcessing = false;
                uni.showToast({
                  title: "权限申请失败",
                  icon: "none",
                });
              });
          } else if (res.platform === "ios") {
            // iOS平台处理
            console.log("当前平台为iOS，启动人脸识别");
            if (this.useH5Face) {
              this.enterH5FaceVerify();
            } else {
              this.startWbFaceVerifyService();
            }
          } else {
            // 其他平台
            console.log("当前平台不支持人脸识别:", res.platform);
            this.isProcessing = false;
            uni.showToast({
              title: "当前平台不支持人脸识别",
              icon: "none",
            });
          }
        },
        fail: (err) => {
          console.error("获取系统信息失败:", err);
          // 降级处理，直接尝试启动
          console.log("降级处理，直接启动人脸识别");
          if (this.useH5Face) {
            this.enterH5FaceVerify();
          } else {
            this.startWbFaceVerifyService();
          }
        },
      });
      // #endif

      // #ifndef APP-PLUS
      // H5或小程序平台采用H5刷脸
      console.log("H5/小程序平台，使用H5刷脸方案");
      this.enterH5FaceVerify();
      // #endif
    },
    getButtonText() {
      if (this.isAuthSuccess) {
        return "认证成功";
      }
      return this.isProcessing ? "处理中..." : "同意授权并继续";
    },
    goToProtocol() {
      // uni.navigateTo({ url: "/pages/protocol/personalInfoAuth" });
      this.showCreditModal = true;
      this.getPrivacyList("MYSELF_INFO_AUTH");
    },
    async getPrivacyList(val) {
      const res = await getArticleDetailByType(val);
      console.log(res);
      if (res.statusCode === 200) {
        this.title = res.data.result.title;
        this.content = res.data.result.content;
      }
    },
    takePhoto() {
      const ctx = uni.createCameraContext();
      ctx.takePhoto({
        quality: "high",
        success: (res) => {
          this.faceImg = res.tempImagePath;
          uni.showLoading({ title: "识别中..." });
          setTimeout(() => {
            uni.hideLoading();
            this.faceResult = "识别成功，身份验证通过";
          }, 1500);
        },
        fail: () => {
          uni.showToast({ title: "拍照失败", icon: "none" });
        },
      });
    },
    goBack() {
      uni.navigateBack();
    },
    closeCreditModal() {
      this.showCreditModal = false;
      // 自动勾选服务授权并登录
      if (!this.checked) {
        this.checked = true;
      }
      // if (this.showAuthModal) {
      //   this.handleAuthLogin();
      // }
    },
    // 获取同盾科技blackBox的方法
    getTrustDeviceBlackBox() {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        // Web 端获取 blackBox
        if (typeof window !== "undefined") {
          // 检查是否已经有 blackBox 数据
          if (window._blackBoxData) {
            resolve(window._blackBoxData);
            return;
          }

          // 如果 window._fmOpt 中有 getinfo 方法，直接调用获取 blackBox
          if (window._fmOpt && window._fmOpt.getinfo) {
            try {
              const blackBox = window._fmOpt.getinfo();
              if (blackBox) {
                window._blackBoxData = blackBox;
                resolve(blackBox);
                return;
              }
            } catch (error) {
              console.error("getinfo方法调用失败:", error);
            }
          }

          // 如果没有数据，等待一段时间后再次检查
          let checkCount = 0;
          const maxChecks = 20; // 最多检查20次（10秒）
          const checkInterval = setInterval(() => {
            checkCount++;

            // 每次检查时也尝试调用 getinfo 方法
            if (window._fmOpt && window._fmOpt.getinfo) {
              try {
                const blackBox = window._fmOpt.getinfo();
                if (blackBox) {
                  clearInterval(checkInterval);
                  window._blackBoxData = blackBox;
                  resolve(blackBox);
                  return;
                }
              } catch (error) {
                console.error("getinfo方法调用失败:", error);
              }
            }

            if (window._blackBoxData) {
              clearInterval(checkInterval);
              resolve(window._blackBoxData);
            } else if (checkCount >= maxChecks) {
              clearInterval(checkInterval);
              reject(new Error("Web端获取blackBox超时"));
            }
          }, 500); // 每500ms检查一次
        } else {
          reject(new Error("Web环境不可用"));
        }
        // #endif

        // #ifdef APP-PLUS
        // APP 端获取 blackBox
        // 初始化同盾科技信任设备
        this.initTrustDevice();

        // 获取blackBox
        const TrustDeviceUniPlugin = uni.requireNativePlugin(
          "TrustDecision-TrustDeviceUniPlugin"
        );
        TrustDeviceUniPlugin.getBlackBoxAsync((ret) => {
          console.log("getBlackBoxAsync,blackBox: " + ret);
          if (ret) {
            resolve(ret);
          } else {
            reject(new Error("获取blackBox失败"));
          }
        });
        // #endif
      });
    },
    // 初始化同盾科技信任设备
    initTrustDevice() {
      // #ifdef H5
      // Web 端初始化同盾科技 SDK
      if (typeof window !== "undefined" && window._fmOpt) {
        console.log("Web端同盾科技SDK已初始化");
        return;
      }

      console.log("开始初始化Web端同盾科技SDK...");

      // 初始化 Web 端同盾科技风控 SDK
      window._fmOpt = {
        partner: "zhenzhang",
        // H5端不需要appKey
        success: function (data) {
          console.log("同盾科技SDK成功获取blackBox:", data);
          // 将 blackBox 存储到全局变量中，供后续使用
          window._blackBoxData = data;
        },
        error: function (error) {
          console.error("同盾科技SDK获取blackBox失败:", error);
        },
      };

      // 动态加载同盾科技 SDK
      var fm = document.createElement("script");
      fm.type = "text/javascript";
      fm.async = true;
      fm.src =
        "https://static.trustdecision.com/tdfp/cn/596d12046434880d22db143cd448c9f7/fm.js" +
        "?t=" +
        (new Date().getTime() / 3600000).toFixed(0);

      // 添加脚本加载事件监听
      fm.onload = function () {
        console.log("同盾科技SDK脚本加载成功");
      };
      fm.onerror = function () {
        console.error("同盾科技SDK脚本加载失败");
      };

      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(fm, s);

      console.log("Web端同盾科技SDK初始化完成");
      // #endif

      // #ifdef APP-PLUS
      // APP 端保持原有实现
      let systemInfo = uni.getSystemInfoSync();
      const platform = systemInfo.platform;
      let appKey;
      if (platform === "android") {
        appKey = "da613cce0c12351f6c54853f84efec37";
      } else if (platform === "ios") {
        appKey = "92842536cabf9d92d91426616161567a";
      }
      const TrustDeviceUniPlugin = uni.requireNativePlugin(
        "TrustDecision-TrustDeviceUniPlugin"
      );
      console.log("TrustDeviceUniPlugin", TrustDeviceUniPlugin);
      var options = {
        partner: "zhenzhang",
        appKey: appKey,
        country: "cn",
      };

      // !!! DEBUG模式下若不设置此参数，app运行会闪退
      // if (process.env.NODE_ENV === "development") {
      //   options["debug"] = true
      // }

      TrustDeviceUniPlugin.initWithOptions(options);
      TrustDeviceUniPlugin.getBlackBoxAsync((ret) => {
        console.log("getBlackBoxAsync,blackBox: " + ret);
      });
      // #endif
    },
    async startWbFaceVerifyService() {
      getFaceParam()
        .then((res) => {
          const data = res.data.result;
          this.userInfo = data;
          console.log("组装参数并调用SDK", data);
          // 组装参数并调用SDK
          face.startWbFaceVerifyService(
            {
              apiVersion: data.apiVersion || "1.0.0",
              appId: data.appId,
              nonce: data.nonce,
              userId: data.userId,
              sign: data.sign,
              orderNo: data.orderNo,
              licence: data.licence,
              faceId: data.faceId,
              compareType: "0", // 仅活体检测
              sdkConfig: {
                showSuccessPage: false,
                showFailurePage: false,
                recordVideo: false,
                checkVideo: false,
                playVoice: false,
                theme: "1",
                customerTipsLoc: "0",
                customerTipsInLive: "",
                customerTipsInUpload: "",
                customerLongTip: "",
                isEnableLog: true,
                windowLevel: "1",
                manualCookie: true,
                useWindowSecene: false,
              },
            },
            (result) => {
              let scene = result.scene;
              if (scene == "wb_face_callback_login_failure") {
                let error = result.res.error;
                let tip = "核验失败，请重新尝试";
                console.log(tip);
                this.isProcessing = false; // 重置处理状态
                this.isAuthSuccess = false; // 重置认证成功状态
                uni.showToast({
                  icon: "none",
                  title: tip,
                });
                return;
              }
              if (scene == "wb_face_callback_verify_result") {
                let res = result.res;
                console.log(res);
                let success = res.success;
                if (success) {
                  // 认证成功后的逻辑
                  getFaceResult(res.orderNo)
                    .then(async (faceResultRes) => {
                      console.log("后端人脸核验结果", faceResultRes);
                      // 可根据需要将结果保存到data或做进一步处理
                      if (faceResultRes.data.code == 200) {
                        console.log("后端人脸成功");
                        console.log("设置认证成功状态:", this.isAuthSuccess);
                        await this.afterFaceSuccess();
                      } else {
                        // 后端核验失败
                        this.isProcessing = false; // 核验失败时重置为可点击状态
                        this.isAuthSuccess = false; // 重置认证成功状态
                        uni.showToast({
                          icon: "none",
                          title: "后端核验失败",
                        });
                      }
                    })
                    .catch((err) => {
                      console.error("获取后端人脸核验结果失败", err);
                      this.isProcessing = false; // 重置处理状态
                      this.isAuthSuccess = false; // 重置认证成功状态
                      uni.showToast({
                        icon: "none",
                        title: "后端核验失败",
                      });
                    });
                } else {
                  let error = res.error;
                  let domain = error.domain;
                  this.isProcessing = false; // 重置处理状态
                  this.isAuthSuccess = false; // 重置认证成功状态
                  if (domain == "WBFaceErrorDomainCompareServer") {
                    uni.showToast({
                      icon: "none",
                      title: "对比失败",
                    });
                    return;
                  }
                  uni.showToast({
                    icon: "none",
                    title: JSON.stringify(error.desc),
                  });
                }
              }
            }
          );
        })
        .catch((err) => {
          this.isProcessing = false; // 重置处理状态
          this.isAuthSuccess = false; // 重置认证成功状态
          uni.showToast({
            icon: "none",
            title: "获取人脸参数失败",
          });
        });
    },
    async enterH5FaceVerify() {
      getFaceParam("H5")
        .then((res) => {
          const data = res.data.result;
          console.log("H5人脸参数", data);

          // 从后端获取的 h5RedirectUrl 构造最终的 H5 刷脸地址
          const thirdurl = "http://192.168.1.4:8080/pages/financial/faceAuth";
          const rawUrl = (data && (data.h5RedirectUrl || data.h5RedirectURI || data.h5Redirect || data.url)) || "";
          if (!rawUrl) {
            throw new Error("缺少h5RedirectUrl");
          }
          const prefixed = rawUrl.startsWith("http") ? rawUrl : `https://${rawUrl}`;
          const connector = prefixed.includes("?") ? "&" : "?";
          const finalUrl = `${prefixed}${connector}url=${encodeURIComponent(thirdurl)}&from=browser`;
          console.log("H5刷脸地址", finalUrl);
          
          // #ifdef H5
          try {
            if (typeof window !== "undefined") {
              // 直接跳转到厂商H5页面，完成后按thirdurl返回
              window.location.href = finalUrl;
            } else {
              throw new Error("window不可用");
            }
          } catch (e) {
            console.error("H5环境跳转失败", e);
            this.isProcessing = false;
            this.isAuthSuccess = false;
            uni.showToast({ icon: "none", title: "当前H5环境无法拉起刷脸" });
          }
          // #endif
        })
        .catch((err) => {
          console.error("获取人脸参数失败", err);
          this.isProcessing = false;
          this.isAuthSuccess = false;
          uni.showToast({ icon: "none", title: "获取人脸参数失败" });
        });
      return;
    },
    async afterFaceSuccess() {
      console.log("设置认证成功状态:", this.isAuthSuccess);
      uni.showToast({
        icon: "none",
        title: "认证成功",
      });
      this.isAuthSuccess = true; // 设置认证成功状态
      console.log("认证成功状态已设置:", this.isAuthSuccess);
      this.isProcessing = true; // 保持禁用状态，防止重复点击
      // 认证成功后调用评估额度接口
      uni.showLoading({
        title: "评估中...",
        mask: true,
      });

      try {
        const blackBox = await this.getTrustDeviceBlackBox();
        console.log("获取到blackBox:", blackBox);
        const assessmentRes = await getAuthAssessmentAmount({
          sCode: blackBox,
        });
        console.log("评估额度结果", assessmentRes);
        this.isProcessing = true; // 保持禁用状态
        this.isAuthSuccess = true; // 设置认证成功状态
        uni.hideLoading();
        if (
          assessmentRes.data &&
          assessmentRes.data.success &&
          assessmentRes.data.code === 200
        ) {
          uni.navigateTo({ url: "/pages/financial/reviewing" });
        } else {
          uni.navigateTo({ url: "/pages/financial/assessmentFailed" });
        }
      } catch (err) {
        console.error("评估流程失败", err);
        uni.hideLoading();
        this.isProcessing = false; // 失败时重置为可点击状态
        this.isAuthSuccess = false; // 重置认证成功状态
        uni.navigateTo({ url: "/pages/financial/assessmentFailed" });
      }
    },
    // 处理H5人脸识别返回时的URL参数
    async handleH5FaceAuthCallback(options) {
      if (this.isProcessing || this.isAuthSuccess) {
        console.log("已在处理中或认证成功，跳过回调处理");
        return;
      }

      const { orderNo, code, message } = this._extractFaceParamsH5(options);
      console.log("H5人脸识别回调参数:", { orderNo, code, message });

      // 缺参数直接返回
      if (!orderNo || code === undefined || code === null) {
        console.log("缺少必要参数：orderNo 或 code");
        return;
      }

      const isSuccess = String(code) === "0";

      // 失败：直接提示并返回
      if (!isSuccess) {
        this.isAuthSuccess = false;
        this.isProcessing = false;
        uni.showToast({
          icon: "none",
          title: message || `核身失败，错误码：${code}`,
        });
        return;
      }

      // 成功：并调用后端校验
      this.isAuthSuccess = true;
      this.isProcessing = true;
      uni.showToast({ icon: "none", title: "核身通过" });

      try {
        const faceResultRes = await getFaceResult(orderNo, "H5");

        if (faceResultRes?.data?.code === 200) {
          await this.afterFaceSuccess(); 
        } else {
          // 后端返回非200，视为校验失败
          throw new Error(faceResultRes?.data?.message || "后端验证失败");
        }
      } catch (e) {
        console.error("H5刷脸后查询后端核验异常", e);
        // 回滚状态，允许重试
        this.isProcessing = false;
        this.isAuthSuccess = false;
        uni.showToast({ icon: "none", title: "后端验证异常" });
      }
    },

    _extractFaceParamsH5(options = {}) {
      const params = { ...options };
      // #ifdef H5
      try {
        const search = (typeof window !== "undefined" && window.location?.search) || "";
        if (search) {
          const urlParams = new URLSearchParams(search);
          for (const key of ["orderNo", "code", "message"]) {
            if (params[key] === undefined || params[key] === null || params[key] === "") {
              const val = urlParams.get(key);
              if (val !== null) params[key] = val;
            }
          }
        }
      } catch (e) {
        console.error("解析 URL 参数失败:", e);
      }
      // #endif

      return {
        orderNo: params.orderNo,
        code: params.code,
        message: params.message,
      };
    },

  },
};
</script>

<style lang="scss" scoped>
@import "@/uni.scss";
page {
  background: #fff;
}
.face-auth-page {
  background: #fff;
}
.auth-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 32rpx 0 32rpx;
}
.auth-avatar-box {
  margin-bottom: 32rpx;
}
.auth-avatar {
  width: 264rpx;
  height: 264rpx;
  border-radius: 50%;
  background: #e6f0fa;
}
.auth-desc-box {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 40rpx;
  width: 686rpx;
  height: 272rpx;
  background: #f7f7f7;
  border-radius: 20rpx;
}
.auth-desc-title {
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.auth-desc-list {
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.2;
  view {
    margin-bottom: 12rpx;
  }
}
.auth-line-box {
  position: fixed;
  bottom: 0;
  width: 746rpx;
  // height: 240rpx;
  background: #ffffff;
  box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(0, 0, 0, 0.04);
  padding: 32rpx 36rpx 22rpx 40rpx;
}
.auth-checkbox-box {
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
  .auth-link {
    margin-left: -20rpx;
    line-height: 1.4;
  }
}
.auth-protocol {
  color: #ff5134;
  margin-left: 8rpx !important;
}
.auth-btn-box {
  width: 100%;
  max-width: 600rpx;
  margin-top: 20rpx;
}
.auth-btn {
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  border-radius: 86rpx 86rpx 86rpx 86rpx;
}
.title-bar_box {
  width: 750rpx;
  background: linear-gradient(180deg, #fbeae1 0%, rgba(251, 234, 225, 0) 100%);
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  padding: 34rpx 32rpx;
}
.title-bar {
  .main-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #444444;
  }
}
.desc {
  margin: 6rpx 0 36rpx 0;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666666;
  image {
    width: 32rpx;
    height: 32rpx;
  }
}
.face-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 40rpx;
}
.camera-box {
  width: 600rpx;
  height: 600rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.face-btns {
  margin-top: 32rpx;
  width: 600rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.face-btn {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
}
.face-result {
  margin-top: 32rpx;
  color: #ff6b35;
  font-size: 28rpx;
  text-align: center;
}
.face-result-title {
  font-weight: bold;
}
.face-img-preview {
  display: flex;
  justify-content: center;
}

.credit-modal-header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 40rpx;
  // padding: 32rpx 0 0 0;
}
.credit-modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222;
}
.credit-modal-close {
  position: absolute;
  right: 32rpx;
  top: 32rpx;
}
.credit-modal-content {
  width: 100%;
  max-height: 716rpx;
  min-height: 400rpx;
  margin: 24rpx 0 0 0;
  background: #f7f7f7;
  border-radius: 12rpx;
  overflow-y: auto;
}
.credit-modal-text {
  font-size: 24rpx;
  color: #333;
  line-height: 1.7;
  padding: 0 32rpx;
}
.credit-modal-btn {
  width: 670rpx;
  height: 100rpx;
  background: linear-gradient(90deg, #ff5235 0%, #ff9500 100%);
  color: #fff;
  font-size: 30rpx;
  border: none;
  border-radius: 78rpx;
  margin: 32rpx 0;
  font-weight: bold;
  line-height: 100rpx;

  &:disabled {
    background: #ccc;
    color: #999;
    cursor: not-allowed;
  }
}
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}
</style>
